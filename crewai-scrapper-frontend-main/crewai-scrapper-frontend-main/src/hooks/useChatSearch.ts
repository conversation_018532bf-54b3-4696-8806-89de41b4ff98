import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../api/store";
import {
  useCreateConversationMutation,
  useAddMessageMutation,
  useChatIntegratedSearchMutation,
} from "../api/services/Chat/ChatService";
import {
  setCurrentConversation,
  setCurrentMessages,
  addMessage,
  setLastSearchTerm,
  setSearching,
  setTyping,
  openChat,
} from "../api/services/Chat/ChatSlice";
import {
  setProducts,
  clearProducts,
  setLoading,
  setError,
} from "../api/services/Products/productSlice";

export interface ChatSearchResult {
  success: boolean;
  searchTerm?: string;
  products?: any[];
  error?: string;
}

export const useChatSearch = () => {
  const dispatch = useDispatch();
  const { activeConversationId, currentConversation } = useSelector(
    (state: RootState) => state.chat
  );

  const [createConversation, { isLoading: isCreating }] = useCreateConversationMutation();
  const [addMessage, { isLoading: isAddingMessage }] = useAddMessageMutation();
  const [chatSearch, { isLoading: isChatSearching }] = useChatIntegratedSearchMutation();

  // Start a new conversation with initial search
  const startConversationWithSearch = useCallback(
    async (initialMessage: string): Promise<ChatSearchResult> => {
      try {
        dispatch(setTyping(true));
        dispatch(setSearching(true));
        dispatch(openChat());

        // Create conversation
        const conversationResult = await createConversation({
          initialMessage,
        }).unwrap();

        dispatch(setCurrentConversation(conversationResult.conversation));
        dispatch(setCurrentMessages(conversationResult.messages));
        dispatch(setLastSearchTerm(conversationResult.searchTerm));

        // Perform search
        const searchResult = await chatSearch({
          conversationId: conversationResult.conversation.id,
          searchTerm: conversationResult.searchTerm,
        }).unwrap();

        // Update products
        dispatch(clearProducts());
        dispatch(setProducts(searchResult.products));

        return {
          success: true,
          searchTerm: searchResult.searchTerm,
          products: searchResult.products,
        };
      } catch (error) {
        console.error("Failed to start conversation with search:", error);
        dispatch(setError("Failed to start conversation"));
        return {
          success: false,
          error: "Failed to start conversation",
        };
      } finally {
        dispatch(setTyping(false));
        dispatch(setSearching(false));
      }
    },
    [createConversation, chatSearch, dispatch]
  );

  // Continue existing conversation with new message
  const continueConversationWithSearch = useCallback(
    async (message: string): Promise<ChatSearchResult> => {
      if (!activeConversationId) {
        return { success: false, error: "No active conversation" };
      }

      try {
        dispatch(setTyping(true));
        dispatch(setSearching(true));

        // Add message to conversation
        const messageResult = await addMessage({
          conversationId: activeConversationId,
          message,
        }).unwrap();

        dispatch(addMessage(messageResult.messages[0])); // User message
        dispatch(addMessage(messageResult.messages[1])); // AI response
        dispatch(setLastSearchTerm(messageResult.searchTerm));

        // Perform search
        const searchResult = await chatSearch({
          conversationId: activeConversationId,
          searchTerm: messageResult.searchTerm,
        }).unwrap();

        // Update products
        dispatch(clearProducts());
        dispatch(setProducts(searchResult.products));

        return {
          success: true,
          searchTerm: searchResult.searchTerm,
          products: searchResult.products,
        };
      } catch (error) {
        console.error("Failed to continue conversation with search:", error);
        dispatch(setError("Failed to continue conversation"));
        return {
          success: false,
          error: "Failed to continue conversation",
        };
      } finally {
        dispatch(setTyping(false));
        dispatch(setSearching(false));
      }
    },
    [activeConversationId, addMessage, chatSearch, dispatch]
  );

  // Generic search function that handles both new and existing conversations
  const performChatSearch = useCallback(
    async (message: string): Promise<ChatSearchResult> => {
      if (activeConversationId) {
        return continueConversationWithSearch(message);
      } else {
        return startConversationWithSearch(message);
      }
    },
    [activeConversationId, continueConversationWithSearch, startConversationWithSearch]
  );

  return {
    performChatSearch,
    startConversationWithSearch,
    continueConversationWithSearch,
    isLoading: isCreating || isAddingMessage || isChatSearching,
    activeConversationId,
    currentConversation,
  };
};
