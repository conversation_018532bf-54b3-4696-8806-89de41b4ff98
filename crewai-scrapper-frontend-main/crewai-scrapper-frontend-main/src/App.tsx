import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Provider } from "react-redux";
import { ToastContainer } from "react-toastify";
import { store } from "./api/store";

import ChatPage from "./pages/user/ChatPage";
import ProductListingPage from "./pages/user/ProductListingPage";
import FavoritesPage from "./pages/user/FavoritesPage";
import AppLayout from "./components/layout/AppLayout";
import LandingPage from "./pages/user/LandingPage";
import ProtectedRoute from "./routes/ProtectedRoute.tsx";

const App = () => {
  return (
    <Provider store={store}>
      <ToastContainer
        position="top-center"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick={false}
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />

      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <Routes>
          <Route path="/about-us" element={<LandingPage />} />
          <Route path="*" element={<Navigate to="/about-us" replace />} />

          <Route element={<AppLayout />}>
            <Route path="/home" element={<ChatPage />} />
            <Route path="/products" element={<ProductListingPage />} />

            <Route
              path="/favorites"
              element={
                <ProtectedRoute>
                  <FavoritesPage />
                </ProtectedRoute>
              }
            />
          </Route>
        </Routes>
      </Router>
    </Provider>
  );
};

export default App;
