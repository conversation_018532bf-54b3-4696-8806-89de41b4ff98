import { fetchBaseQuery } from "@reduxjs/toolkit/query";

const { VITE_BACKEND_BASE_URL } = import.meta.env;

export const BASE_URL = VITE_BACKEND_BASE_URL;

export const baseQuery = fetchBaseQuery({
  baseUrl: BASE_URL,
  prepareHeaders: (headers) => {
    const accessToken = localStorage.getItem("accessToken") || "";

    if (accessToken) {
      headers.set("Authorization", `Bearer ${accessToken}`);
    }

    return headers;
  },
});
