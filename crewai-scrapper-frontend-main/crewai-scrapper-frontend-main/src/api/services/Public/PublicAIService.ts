import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export interface EnhanceSearchRequest {
  searchTerm: string;
}

export interface EnhanceSearchResponse {
  originalTerm: string;
  enhancedTerm: string;
  suggestion: string;
  aiEnhanced?: boolean;
}

export const publicAIApi = createApi({
  reducerPath: "publicAIApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL || "http://localhost:4000/api",
  }),
  
  endpoints: (builder) => ({
    enhanceSearchTerm: builder.mutation<EnhanceSearchResponse, EnhanceSearchRequest>({
      query: (body) => ({
        url: "/public/enhance-search",
        method: "POST",
        body,
      }),
    }),
  }),
});

export const { useEnhanceSearchTermMutation } = publicAIApi;
