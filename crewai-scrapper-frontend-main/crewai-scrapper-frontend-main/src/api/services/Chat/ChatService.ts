import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../../interceptor-util";

export interface Message {
  id: string;
  content: string;
  role: "user" | "assistant" | "system";
  searchTermGenerated?: string;
  metadata?: any;
  createdAt: string;
}

export interface Conversation {
  id: string;
  title?: string;
  initialSearchTerm: string;
  lastSearchTerm?: string;
  context?: any;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
  messageCount?: number;
  lastMessage?: Message;
}

export interface CreateConversationRequest {
  initialMessage: string;
}

export interface CreateConversationResponse {
  conversation: Conversation;
  searchTerm: string;
  messages: Message[];
}

export interface AddMessageRequest {
  message: string;
}

export interface AddMessageResponse {
  searchTerm: string;
  messages: Message[];
}

export interface ChatSearchRequest {
  conversationId: string;
  searchTerm: string;
}

export interface ChatSearchResponse {
  searchTerm: string;
  products: any[];
  aiResponse: string;
  conversationId: string;
}

export const chatApi = createApi({
  reducerPath: "chatApi",
  keepUnusedDataFor: 300, // Keep chat data for 5 minutes
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Conversation", "Message"],

  endpoints: (builder) => ({
    // Create new conversation
    createConversation: builder.mutation<CreateConversationResponse, CreateConversationRequest>({
      query: (body) => ({
        url: "/chat/conversations",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Conversation"],
    }),

    // Add message to conversation
    addMessage: builder.mutation<AddMessageResponse, { conversationId: string } & AddMessageRequest>({
      query: ({ conversationId, ...body }) => ({
        url: `/chat/conversations/${conversationId}/messages`,
        method: "POST",
        body,
      }),
      invalidatesTags: (result, error, { conversationId }) => [
        { type: "Conversation", id: conversationId },
        { type: "Message", id: conversationId },
      ],
    }),

    // Get specific conversation with messages
    getConversation: builder.query<Conversation, string>({
      query: (conversationId) => ({
        url: `/chat/conversations/${conversationId}`,
        method: "GET",
      }),
      providesTags: (result, error, conversationId) => [
        { type: "Conversation", id: conversationId },
        { type: "Message", id: conversationId },
      ],
    }),

    // Get user's conversations
    getUserConversations: builder.query<{
      conversations: Conversation[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 10 } = {}) => ({
        url: `/chat/conversations?page=${page}&limit=${limit}`,
        method: "GET",
      }),
      providesTags: ["Conversation"],
    }),

    // Delete conversation
    deleteConversation: builder.mutation<void, string>({
      query: (conversationId) => ({
        url: `/chat/conversations/${conversationId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Conversation"],
    }),

    // Chat-integrated search
    chatIntegratedSearch: builder.mutation<ChatSearchResponse, ChatSearchRequest>({
      query: (body) => ({
        url: "/scraping/chat-search",
        method: "POST",
        body,
      }),
      invalidatesTags: (result, error, { conversationId }) => [
        { type: "Conversation", id: conversationId },
        { type: "Message", id: conversationId },
      ],
    }),
  }),
});

export const {
  useCreateConversationMutation,
  useAddMessageMutation,
  useGetConversationQuery,
  useGetUserConversationsQuery,
  useDeleteConversationMutation,
  useChatIntegratedSearchMutation,
  useLazyGetConversationQuery,
  useLazyGetUserConversationsQuery,
} = chatApi;
