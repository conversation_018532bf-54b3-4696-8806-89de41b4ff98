import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Conversation, Message } from "./ChatService";

interface ChatState {
  // Current active conversation
  activeConversationId: string | null;
  
  // Chat UI state
  isChatOpen: boolean;
  isTyping: boolean;
  
  // Current conversation data (for quick access)
  currentConversation: Conversation | null;
  currentMessages: Message[];
  
  // Search integration
  lastSearchTerm: string | null;
  isSearching: boolean;
  
  // UI preferences
  chatPosition: "right" | "left";
  chatWidth: number;
}

const initialState: ChatState = {
  activeConversationId: null,
  isChatOpen: true, // Open by default
  isTyping: false,
  currentConversation: null,
  currentMessages: [],
  lastSearchTerm: null,
  isSearching: false,
  chatPosition: "left", // Position on left
  chatWidth: 256, // Minimized width
};

const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    // <PERSON>t UI controls
    openChat: (state) => {
      state.isChatOpen = true;
    },
    
    closeChat: (state) => {
      state.isChatOpen = false;
    },
    
    toggleChat: (state) => {
      state.isChatOpen = !state.isChatOpen;
    },
    
    // Conversation management
    setActiveConversation: (state, action: PayloadAction<string | null>) => {
      state.activeConversationId = action.payload;
    },
    
    setCurrentConversation: (state, action: PayloadAction<Conversation | null>) => {
      state.currentConversation = action.payload;
      if (action.payload) {
        state.activeConversationId = action.payload.id;
      }
    },
    
    setCurrentMessages: (state, action: PayloadAction<Message[]>) => {
      state.currentMessages = action.payload;
    },
    
    addMessage: (state, action: PayloadAction<Message>) => {
      state.currentMessages.push(action.payload);
    },
    
    addMessages: (state, action: PayloadAction<Message[]>) => {
      state.currentMessages.push(...action.payload);
    },
    
    // Typing indicator
    setTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    
    // Search integration
    setLastSearchTerm: (state, action: PayloadAction<string | null>) => {
      state.lastSearchTerm = action.payload;
    },
    
    setSearching: (state, action: PayloadAction<boolean>) => {
      state.isSearching = action.payload;
    },
    
    // UI preferences
    setChatPosition: (state, action: PayloadAction<"right" | "left">) => {
      state.chatPosition = action.payload;
    },
    
    setChatWidth: (state, action: PayloadAction<number>) => {
      state.chatWidth = Math.max(300, Math.min(600, action.payload));
    },
    
    // Reset chat state
    resetChat: (state) => {
      state.activeConversationId = null;
      state.currentConversation = null;
      state.currentMessages = [];
      state.lastSearchTerm = null;
      state.isTyping = false;
      state.isSearching = false;
    },
    
    // Start new conversation flow
    startNewConversation: (state) => {
      state.activeConversationId = null;
      state.currentConversation = null;
      state.currentMessages = [];
      state.isChatOpen = true;
    },
  },
});

export const {
  openChat,
  closeChat,
  toggleChat,
  setActiveConversation,
  setCurrentConversation,
  setCurrentMessages,
  addMessage,
  addMessages,
  setTyping,
  setLastSearchTerm,
  setSearching,
  setChatPosition,
  setChatWidth,
  resetChat,
  startNewConversation,
} = chatSlice.actions;

export default chatSlice.reducer;
