import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface User {
  id: string;
  email: string;
  isPremium?: boolean;
  credits?: number;
  name?: string;
}

interface AuthState {
  user: User | null;
  accessToken: string | null;
  isAuthenticated: boolean;
}

const userRaw = localStorage.getItem("user");
const user = userRaw && userRaw !== "undefined" ? JSON.parse(userRaw) : null;

const accessTokenRaw = localStorage.getItem("accessToken");
const accessToken = accessTokenRaw && accessTokenRaw !== "undefined" ? accessTokenRaw : null;

const initialState: AuthState = {
  user,
  accessToken,
  isAuthenticated: !!user && !!accessToken,
};

interface CredentialsPayload {
  user: User;
  accessToken: string;
}

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setCredentials: (state: AuthState, { payload }: PayloadAction<CredentialsPayload>) => {
      state.user = payload.user;
      state.accessToken = payload.accessToken;
      localStorage.setItem("user", JSON.stringify(payload.user));
      localStorage.setItem("accessToken", payload.accessToken);
      state.isAuthenticated = true;
    },

    logoutUser: (state: AuthState) => {
      state.user = null;
      state.accessToken = null;
      state.isAuthenticated = false;
      localStorage.clear();
    },
  },
});

export const { setCredentials, logoutUser } = authSlice.actions;
export default authSlice.reducer;
