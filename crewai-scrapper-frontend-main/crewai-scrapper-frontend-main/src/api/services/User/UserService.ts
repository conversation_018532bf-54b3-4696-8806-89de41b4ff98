import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../../interceptor-util";

export const userApi = createApi({
  reducerPath: "userApi",
  keepUnusedDataFor: 0,
  baseQuery: baseQueryWithReauth,

  endpoints: (builder) => ({
    logout: builder.mutation({
      query: () => ({
        url: "logout",
        method: "POST",
      }),
    }),
  }),
});

export const { useLogoutMutation } = userApi;
