import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Product } from "../../../types/Product";

interface ProductState {
  products: Product[];
  selectedProducts: string[];
  favorites: string[];
  loading: boolean;
  error: string | null;
  dynamicMaxPrice: number; // NEW: for dynamic max price
}

const initialState: ProductState = {
  products: [],
  selectedProducts: [],
  favorites: [],
  loading: false,
  error: null,
  dynamicMaxPrice: 10000,
};

const productSlice = createSlice({
  name: "product",
  initialState,
  reducers: {
    // Toggle favorite product by ID
    toggleFavorite: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      if (state.favorites.includes(productId)) {
        state.favorites = state.favorites.filter((id) => id !== productId);
      } else {
        state.favorites.push(productId);
      }
    },

    // Toggle selected product by ID
    toggleSelection: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      if (state.selectedProducts.includes(productId)) {
        state.selectedProducts = state.selectedProducts.filter(
          (id) => id !== productId
        );
      } else {
        state.selectedProducts.push(productId);
      }
    },

    // Set the full product list (used on initial load or reset)
    setProducts: (state, action: PayloadAction<Product[]>) => {
      state.products = action.payload;
      state.loading = false;
      state.error = null;
    },

    // Append new products (used in pagination)
    addProducts: (state, action: PayloadAction<Product[]>) => {
      state.products = [...state.products, ...action.payload];
      state.loading = false;
      state.error = null;
    },

    // Clear all products and related states
    clearProducts: (state) => {
      state.products = [];
      state.selectedProducts = [];
      state.error = null;
      state.loading = false;
    },

    // Set loading status
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    // Set error message
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.loading = false;
    },

    // Set dynamic max price (used for slider max limit)
    setDynamicMaxPrice: (state, action: PayloadAction<number>) => {
      state.dynamicMaxPrice = action.payload;
    },
  },
});

export const {
  toggleFavorite,
  toggleSelection,
  setProducts,
  addProducts,
  clearProducts,
  setLoading,
  setError,
  setDynamicMaxPrice,
} = productSlice.actions;

export default productSlice.reducer;
