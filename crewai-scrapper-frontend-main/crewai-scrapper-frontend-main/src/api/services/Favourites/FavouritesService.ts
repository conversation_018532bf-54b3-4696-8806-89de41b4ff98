import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "../../api-utils";

export const favouritesApi = createApi({
  reducerPath: "favouritesApi",
  baseQuery,
  tagTypes: ["Favourites"],
  endpoints: (builder) => ({
    // et all favourites
    getFavourites: builder.query({
      query: () => "/favourites",
      providesTags: ["Favourites"],
    }),

    //Add a product to favourites
    addToFavourites: builder.mutation({
      query: (productId: string) => ({
        url: "/favourites",
        method: "POST",
        body: { productId },
      }),
      invalidatesTags: ["Favourites"],
    }),

    //Remove product from favourites
    removeFromFavourites: builder.mutation({
      query: (productId: string) => ({
        url: `/favourites/${productId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Favourites"],
    }),
  }),
});

export const {
  useGetFavouritesQuery,
  useAddToFavouritesMutation,
  useRemoveFromFavouritesMutation,
} = favouritesApi;
