import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "../../api-utils";

export const historyApi = createApi({
  reducerPath: "historyApi",
  baseQuery,
  tagTypes: ["History"],
  endpoints: (builder) => ({
    getSearchHistory: builder.query({
      query: () => "history",
      providesTags: ["History"],
    }),
    deleteSearchHistory: builder.mutation({
      query: (id) => ({
        url: `history/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["History"],
    }),

    // 🧹 Clear all history
    clearSearchHistory: builder.mutation({
      query: () => ({
        url: "history",
        method: "DELETE",
      }),
      invalidatesTags: ["History"],
    }),
  }),
});

export const {
  useGetSearchHistoryQuery,
  useDeleteSearchHistoryMutation,
  useClearSearchHistoryMutation,
} = historyApi;
