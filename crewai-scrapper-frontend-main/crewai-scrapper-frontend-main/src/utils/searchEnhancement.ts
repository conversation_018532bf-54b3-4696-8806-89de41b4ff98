// Search enhancement utilities for AI-powered search optimization

export interface SearchContext {
  previousSearches: string[];
  userPreferences: {
    priceRange?: { min: number; max: number };
    categories?: string[];
    brands?: string[];
  };
  conversationHistory: Array<{
    role: "user" | "assistant";
    content: string;
    searchTerm?: string;
  }>;
}

export interface EnhancedSearchQuery {
  originalQuery: string;
  enhancedQuery: string;
  searchTerms: string[];
  filters: {
    category?: string;
    priceRange?: { min: number; max: number };
    brand?: string;
    gender?: "men" | "women" | "unisex" | "kids";
  };
  confidence: number;
}

// Extract search intent from user message
export const extractSearchIntent = (message: string): {
  intent: "search" | "refine" | "filter" | "compare" | "question";
  entities: string[];
  modifiers: string[];
} => {
  const lowerMessage = message.toLowerCase();
  
  // Intent detection patterns
  const searchPatterns = [
    /find|search|look for|show me|i want|i need|get me/,
    /where can i|how to find|looking for/
  ];
  
  const refinePatterns = [
    /but|however|instead|actually|rather|prefer/,
    /more|less|better|cheaper|expensive|different/
  ];
  
  const filterPatterns = [
    /under|over|between|from|to|\$|price|cost/,
    /color|size|brand|material|style/
  ];
  
  const comparePatterns = [
    /compare|vs|versus|difference|better than|similar to/
  ];
  
  // Determine intent
  let intent: "search" | "refine" | "filter" | "compare" | "question" = "search";
  
  if (refinePatterns.some(pattern => pattern.test(lowerMessage))) {
    intent = "refine";
  } else if (filterPatterns.some(pattern => pattern.test(lowerMessage))) {
    intent = "filter";
  } else if (comparePatterns.some(pattern => pattern.test(lowerMessage))) {
    intent = "compare";
  } else if (lowerMessage.includes("?") || lowerMessage.startsWith("what") || lowerMessage.startsWith("how")) {
    intent = "question";
  }
  
  // Extract entities (product names, brands, etc.)
  const entities = extractEntities(message);
  
  // Extract modifiers (adjectives, price indicators, etc.)
  const modifiers = extractModifiers(message);
  
  return { intent, entities, modifiers };
};

// Extract product entities from text
const extractEntities = (text: string): string[] => {
  const entities: string[] = [];
  const lowerText = text.toLowerCase();
  
  // Common product categories
  const productCategories = [
    "shoes", "sneakers", "boots", "sandals", "heels",
    "shirt", "t-shirt", "dress", "pants", "jeans", "jacket", "coat",
    "bag", "handbag", "backpack", "purse", "wallet",
    "jewelry", "necklace", "earrings", "ring", "bracelet",
    "watch", "sunglasses", "hat", "cap",
    "phone", "laptop", "headphones", "charger",
    "makeup", "skincare", "perfume", "shampoo"
  ];
  
  // Brand names (common ones)
  const brands = [
    "nike", "adidas", "apple", "samsung", "gucci", "prada",
    "levi's", "zara", "h&m", "uniqlo", "coach", "michael kors"
  ];
  
  productCategories.forEach(category => {
    if (lowerText.includes(category)) {
      entities.push(category);
    }
  });
  
  brands.forEach(brand => {
    if (lowerText.includes(brand)) {
      entities.push(brand);
    }
  });
  
  return entities;
};

// Extract modifiers (price, quality, style indicators)
const extractModifiers = (text: string): string[] => {
  const modifiers: string[] = [];
  const lowerText = text.toLowerCase();
  
  // Price modifiers
  const priceModifiers = ["cheap", "expensive", "affordable", "budget", "premium", "luxury"];
  // Quality modifiers
  const qualityModifiers = ["high quality", "good quality", "durable", "reliable"];
  // Style modifiers
  const styleModifiers = ["casual", "formal", "sporty", "elegant", "trendy", "vintage"];
  // Size modifiers
  const sizeModifiers = ["small", "medium", "large", "xl", "xxl"];
  
  [...priceModifiers, ...qualityModifiers, ...styleModifiers, ...sizeModifiers].forEach(modifier => {
    if (lowerText.includes(modifier)) {
      modifiers.push(modifier);
    }
  });
  
  return modifiers;
};

// Enhance search query based on context and intent
export const enhanceSearchQuery = (
  originalQuery: string,
  context: SearchContext
): EnhancedSearchQuery => {
  const { intent, entities, modifiers } = extractSearchIntent(originalQuery);
  
  let enhancedQuery = originalQuery;
  let confidence = 0.7; // Base confidence
  
  // Enhance based on entities
  if (entities.length > 0) {
    enhancedQuery = entities.join(" ");
    confidence += 0.1;
  }
  
  // Add context from previous searches
  if (context.previousSearches.length > 0) {
    const lastSearch = context.previousSearches[context.previousSearches.length - 1];
    if (intent === "refine" && lastSearch) {
      // Combine with previous search for refinement
      enhancedQuery = `${lastSearch} ${enhancedQuery}`;
      confidence += 0.1;
    }
  }
  
  // Extract filters
  const filters: EnhancedSearchQuery["filters"] = {};
  
  // Price extraction
  const priceMatch = originalQuery.match(/\$(\d+)(?:\s*-\s*\$?(\d+))?/);
  if (priceMatch) {
    const min = parseInt(priceMatch[1]);
    const max = priceMatch[2] ? parseInt(priceMatch[2]) : min * 2;
    filters.priceRange = { min, max };
    confidence += 0.1;
  }
  
  // Gender detection
  if (/\b(men|man|male|boy)\b/i.test(originalQuery)) {
    filters.gender = "men";
    confidence += 0.05;
  } else if (/\b(women|woman|female|girl|lady)\b/i.test(originalQuery)) {
    filters.gender = "women";
    confidence += 0.05;
  } else if (/\b(kid|child|children)\b/i.test(originalQuery)) {
    filters.gender = "kids";
    confidence += 0.05;
  }
  
  // Category detection
  const categoryMap: { [key: string]: string } = {
    "shoes": "footwear",
    "sneakers": "footwear",
    "boots": "footwear",
    "shirt": "clothing",
    "dress": "clothing",
    "pants": "clothing",
    "bag": "bags",
    "handbag": "bags",
    "jewelry": "jewelry",
    "watch": "jewelry"
  };
  
  for (const entity of entities) {
    if (categoryMap[entity]) {
      filters.category = categoryMap[entity];
      confidence += 0.05;
      break;
    }
  }
  
  return {
    originalQuery,
    enhancedQuery: enhancedQuery.trim(),
    searchTerms: [enhancedQuery, ...entities].filter(Boolean),
    filters,
    confidence: Math.min(confidence, 1.0)
  };
};

// Generate search suggestions based on context
export const generateSearchSuggestions = (
  currentQuery: string,
  context: SearchContext
): string[] => {
  const suggestions: string[] = [];
  const { entities, modifiers } = extractSearchIntent(currentQuery);
  
  // Add entity-based suggestions
  entities.forEach(entity => {
    suggestions.push(`${entity} for women`);
    suggestions.push(`${entity} for men`);
    suggestions.push(`affordable ${entity}`);
    suggestions.push(`premium ${entity}`);
  });
  
  // Add modifier combinations
  if (entities.length > 0 && modifiers.length > 0) {
    entities.forEach(entity => {
      modifiers.forEach(modifier => {
        suggestions.push(`${modifier} ${entity}`);
      });
    });
  }
  
  // Add context-based suggestions
  if (context.previousSearches.length > 0) {
    const lastSearch = context.previousSearches[context.previousSearches.length - 1];
    suggestions.push(`similar to ${lastSearch}`);
    suggestions.push(`alternative to ${lastSearch}`);
  }
  
  return suggestions.slice(0, 5); // Return top 5 suggestions
};
