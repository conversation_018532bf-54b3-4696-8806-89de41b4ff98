export interface SearchHistoryItem {
  term: string;
  timestamp: number;
}

export const saveSearchToHistory = (term: string) => {
  if (!term.trim()) return;

  try {
    const existingHistory = localStorage.getItem("searchHistory");
    let history: SearchHistoryItem[] = existingHistory
      ? JSON.parse(existingHistory)
      : [];

    // Avoid duplicates
    const normalizedTerm = term.trim().toLowerCase();
    if (!history.some((item) => item.term.toLowerCase() === normalizedTerm)) {
      history.unshift({
        term: term.trim(),
        timestamp: Date.now(),
      });

      // Limit to 50 most recent searches
      if (history.length > 50) {
        history = history.slice(0, 50);
      }

      localStorage.setItem("searchHistory", JSON.stringify(history));
      window.dispatchEvent(new Event("searchHistoryUpdate"));
    }
  } catch (error) {
    console.error("Error saving search history:", error);
  }
};
