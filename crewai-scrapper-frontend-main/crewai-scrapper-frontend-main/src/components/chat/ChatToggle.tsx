import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../api/store";
import { toggleChat, startNewConversation } from "../../api/services/Chat/ChatSlice";
import { MessageCircle, X } from "lucide-react";

interface ChatToggleProps {
  className?: string;
}

const ChatToggle: React.FC<ChatToggleProps> = ({ className = "" }) => {
  const dispatch = useDispatch();
  const { isChatOpen, activeConversationId, currentMessages } = useSelector((state: RootState) => state.chat);

  const handleToggle = () => {
    if (!isChatOpen && !activeConversationId) {
      // If opening chat and no active conversation, start new one
      dispatch(startNewConversation());
    } else {
      dispatch(toggleChat());
    }
  };

  const hasActiveConversation = activeConversationId && currentMessages.length > 0;
  const hasUnreadConversation = hasActiveConversation && !isChatOpen;

  return (
    <div className="fixed bottom-6 right-6 z-40">
      <button
        onClick={handleToggle}
        className={`relative p-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full shadow-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 hover:scale-110 ${className}`}
        title={isChatOpen ? "Close AI Assistant" : "Open AI Assistant"}
      >
        {isChatOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <MessageCircle className="h-6 w-6" />
        )}

        {/* Badge for active conversation */}
        {hasUnreadConversation && (
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          </div>
        )}

        {/* Notification count for multiple messages */}
        {hasUnreadConversation && currentMessages.length > 2 && (
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg">
            {currentMessages.length > 9 ? '9+' : currentMessages.length}
          </div>
        )}
      </button>
    </div>
  );
};

export default ChatToggle;
