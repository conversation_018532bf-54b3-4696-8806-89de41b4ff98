import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../api/store";
import {
  useGetUserConversationsQuery,
  useDeleteConversationMutation,
} from "../../api/services/Chat/ChatService";
import {
  setActiveConversation,
  startNewConversation,
} from "../../api/services/Chat/ChatSlice";
import { MessageCircle, Plus, Trash2, Clock } from "lucide-react";

interface ConversationListProps {
  onSelectConversation?: () => void;
}

const ConversationList: React.FC<ConversationListProps> = ({ onSelectConversation }) => {
  const dispatch = useDispatch();
  const { activeConversationId } = useSelector((state: RootState) => state.chat);

  const { data: conversationsData, isLoading } = useGetUserConversationsQuery({
    page: 1,
    limit: 20,
  });

  const [deleteConversation] = useDeleteConversationMutation();

  const handleSelectConversation = (conversationId: string) => {
    dispatch(setActiveConversation(conversationId));
    onSelectConversation?.();
  };

  const handleNewConversation = () => {
    dispatch(startNewConversation());
    onSelectConversation?.();
  };

  const handleDeleteConversation = async (conversationId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm("Are you sure you want to delete this conversation?")) {
      try {
        await deleteConversation(conversationId).unwrap();
        if (activeConversationId === conversationId) {
          dispatch(startNewConversation());
        }
      } catch (error) {
        console.error("Failed to delete conversation:", error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: "short" });
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="p-3 border-b border-gray-200 bg-white">
        <button
          onClick={handleNewConversation}
          className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span className="text-sm font-medium">New Chat</span>
        </button>
      </div>

      {/* Conversations list */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 text-center text-gray-500">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-sm">Loading conversations...</p>
          </div>
        ) : conversationsData?.conversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No conversations yet</p>
            <p className="text-xs mt-1">Start a new chat to begin</p>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {conversationsData?.conversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => handleSelectConversation(conversation.id)}
                className={`group relative p-3 rounded-lg cursor-pointer transition-colors ${
                  activeConversationId === conversation.id
                    ? "bg-purple-100 border border-purple-200"
                    : "bg-white hover:bg-gray-50 border border-transparent"
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {conversation.title || "Untitled Conversation"}
                    </h4>
                    
                    {conversation.lastMessage && (
                      <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                        {truncateText(conversation.lastMessage.content, 60)}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-2 mt-2 text-xs text-gray-400">
                      <Clock className="h-3 w-3" />
                      <span>{formatDate(conversation.updatedAt)}</span>
                      {conversation.messageCount && (
                        <>
                          <span>•</span>
                          <span>{conversation.messageCount} messages</span>
                        </>
                      )}
                    </div>
                  </div>
                  
                  <button
                    onClick={(e) => handleDeleteConversation(conversation.id, e)}
                    className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-500 transition-all"
                    title="Delete conversation"
                  >
                    <Trash2 className="h-3 w-3" />
                  </button>
                </div>
                
                {/* Search term indicator */}
                {conversation.lastSearchTerm && (
                  <div className="mt-2 text-xs text-purple-600 bg-purple-50 px-2 py-1 rounded">
                    Last search: {truncateText(conversation.lastSearchTerm, 30)}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationList;
