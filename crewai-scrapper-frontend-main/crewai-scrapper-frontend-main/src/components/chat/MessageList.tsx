import React, { useEffect, useRef } from "react";
import { Message } from "../../api/services/Chat/ChatService";
import { Bot, User, Search, Loader2 } from "lucide-react";

interface MessageListProps {
  messages: Message[];
  isTyping?: boolean;
  isSearching?: boolean;
}

const MessageList: React.FC<MessageListProps> = ({ messages, isTyping, isSearching }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping, isSearching]);

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="max-w-4xl mx-auto px-4 py-6 space-y-6">
      {messages.length === 0 && !isTyping && !isSearching && (
        <div className="text-center text-gray-500 mt-8">
          <Bot className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-lg font-medium">Welcome to AI Shopping Assistant!</p>
          <p className="text-sm mt-2">
            Start by telling me what products you're looking for. I'll help you find the best options and refine your search.
          </p>
        </div>
      )}

      {messages.length === 0 && isSearching && (
        <div className="text-center text-gray-500 mt-8">
          <Bot className="h-12 w-12 mx-auto mb-4 text-purple-600 animate-pulse" />
          <p className="text-lg font-medium">AI is processing your search...</p>
          <p className="text-sm mt-2">
            Creating optimized search terms and finding the best products for you.
          </p>
        </div>
      )}

      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex items-start space-x-4 ${
            message.role === "user" ? "flex-row-reverse space-x-reverse" : ""
          }`}
        >
          {/* Avatar */}
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
            message.role === "user"
              ? "bg-gradient-to-r from-purple-600 to-pink-600"
              : "bg-gray-100"
          }`}>
            {message.role === "assistant" ? (
              <Bot className="h-4 w-4 text-gray-600" />
            ) : (
              <User className="h-4 w-4 text-white" />
            )}
          </div>

          {/* Message content */}
          <div className={`flex-1 ${message.role === "user" ? "text-right" : ""}`}>
            <div className={`inline-block max-w-[85%] rounded-2xl px-4 py-3 ${
              message.role === "user"
                ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white"
                : "bg-gray-100 text-gray-800"
            }`}>
              <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>

              {/* Show search term if generated */}
              {message.searchTermGenerated && (
                <div className={`mt-3 flex items-center space-x-2 text-xs ${
                  message.role === "user" ? "text-purple-100" : "text-gray-600"
                } bg-black bg-opacity-10 rounded-lg px-2 py-1`}>
                  <Search className="h-3 w-3" />
                  <span>Searching: "{message.searchTermGenerated}"</span>
                </div>
              )}
            </div>

            {/* Timestamp */}
            <div className={`mt-1 text-xs text-gray-500 ${
              message.role === "user" ? "text-right" : "text-left"
            }`}>
              {formatTime(message.createdAt)}
            </div>
          </div>
        </div>
      ))}

      {/* Typing indicator */}
      {isTyping && (
        <div className="flex justify-start">
          <div className="bg-gray-100 text-gray-800 rounded-lg px-4 py-2 max-w-[80%]">
            <div className="flex items-center space-x-2">
              <Bot className="h-4 w-4" />
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search indicator */}
      {isSearching && (
        <div className="flex justify-center">
          <div className="bg-blue-100 text-blue-800 rounded-lg px-4 py-2 flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">Searching for products...</span>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default MessageList;
