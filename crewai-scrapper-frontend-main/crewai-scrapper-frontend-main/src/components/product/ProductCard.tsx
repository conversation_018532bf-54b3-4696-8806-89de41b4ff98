import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Heart, Star, Check } from "lucide-react";
import { Product } from "../../types/Product";
import { RootState } from "../../api/store";
import { toggleFavorite } from "../../api/services/Products/productSlice";
import AuthModal from "../common/AuthModal";
import {
  useAddToFavouritesMutation,
  useRemoveFromFavouritesMutation,
} from "../../api/services/Favourites/FavouritesService";

interface ProductCardProps {
  product: Product;
  onFavoriteChange?: (productId: string, isFavorite: boolean) => void;
  isInFavoritePage?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onFavoriteChange,
  isInFavoritePage = false,
}) => {
  const dispatch = useDispatch();
  const { favorites } = useSelector((state: RootState) => state.product);
  const [step, setStep] = useState<
    "register" | "otp" | "login" | "forget-password" | "reset-password"
  >("login");
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [addToFavourites] = useAddToFavouritesMutation();
  const [removeFromFavourites] = useRemoveFromFavouritesMutation();

  // On favorites page, always true; otherwise, check Redux
  const isFavorite = isInFavoritePage ? true : favorites.includes(product.id);

  const handleFavoriteClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }

    try {
      if (isFavorite) {
        await removeFromFavourites(product.id);
      } else {
        await addToFavourites(product.id);
      }
    } catch (err) {
      console.log("Error updating favourite", err);
    }

    if (!isInFavoritePage) {
      dispatch(toggleFavorite(product.id));
    }

    if (onFavoriteChange) {
      onFavoriteChange(product.id, !isFavorite);
    }
  };

  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const stars = [];

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Star
          key="half"
          className="w-3 h-3 fill-yellow-400/50 text-yellow-400"
        />
      );
    }

    for (let i = 0; i < 5 - Math.ceil(rating); i++) {
      stars.push(<Star key={`empty-${i}`} className="w-3 h-3 text-gray-300" />);
    }

    return stars;
  };

  return (
    <div className="bg-white rounded-2xl border border-gray-100 hover:border-pink-300 transition-all duration-300 hover:shadow-xl group overflow-hidden">
      <div className="relative">
        {/* Product Image */}
        <img
          src={product.imageUrl}
          alt={product.title}
          className="w-full h-52 object-cover bg-gray-50 rounded-t-2xl transition-transform duration-300 group-hover:scale-105"
        />

        {/* Favorite Button */}
        <button
          onClick={handleFavoriteClick}
          className={`absolute top-3 right-3 p-2 rounded-full shadow-md transition-all duration-200 ${
            isFavorite
              ? "bg-red-100 text-red-500 hover:bg-red-200"
              : "bg-white/90 text-gray-400 hover:bg-white hover:text-red-500"
          } backdrop-blur`}
        >
          <Heart className={`w-5 h-5 ${isFavorite ? "fill-current" : ""}`} />
        </button>

        {/* Price Badge */}
        <div className="absolute top-3 left-3 bg-gradient-to-br from-pink-500 to-pink-400 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-sm">
          {product.price}
        </div>
      </div>

      {/* Content Section */}
      <div className="p-4 space-y-4">
        {/* Title */}
        <h3 className="text-sm font-semibold text-gray-900 group-hover:text-pink-600 transition-colors duration-200 line-clamp-2">
          {product.title}
        </h3>

        {/* Platform & MOQ */}
        <div className="text-xs text-gray-500 flex justify-between">
          <span className="font-medium">
            Platform: <span className="text-gray-700">{product.platform}</span>
          </span>
          <span>{product.minimumOrderQuantity}</span>
        </div>

        {/* Vendor Info */}
        <div className="text-xs bg-gray-50 rounded-lg p-3 border border-gray-100 space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-pink-600 font-medium truncate">
              {product.vendor.name}
            </span>
            {product.vendor.isVerified && (
              <div className="flex items-center text-green-500 font-medium">
                <Check className="w-4 h-4 mr-1" />
                Verified
              </div>
            )}
          </div>
          <div className="text-gray-600">
            <p>Products: {product.vendor.numOfProducts}</p>
            <a
              href={product.vendor.websiteUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline"
            >
              Visit Website
            </a>
          </div>
        </div>

        {/* Rating & Category */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {renderStars(product.rating)}
            <span className="text-xs text-gray-600">{product.rating}</span>
          </div>
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => {
          setShowAuthModal(false);
          setStep("login");
        }}
        step={step}
        setStep={setStep}
      />
    </div>
  );
};

export default ProductCard;
