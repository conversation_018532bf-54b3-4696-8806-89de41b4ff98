import React from "react";
import { useSelector } from "react-redux"; // Keep useSelector if still needed for loading/error states
import { Package } from "lucide-react";
import { RootState } from "../../api/store"; // Keep if useSelector is used for loading/error
import ProductCard from "./ProductCard";
import { Product } from "../../types/Product";

// Define the props interface for ProductGrid
interface ProductGridProps {
  products: Product[]; // Now expects a 'products' array prop passed from parent
  searchTerm?: string;
}

const ProductGrid: React.FC<ProductGridProps> = ({ products, searchTerm }) => {
  // We are still getting loading and error from Redux, as these states are managed
  // by ProductListingPage and propagated to the store. ProductGrid only consumes them.
  const { loading, error } = useSelector((state: RootState) => state.product);

  // Display loading indicator if products are still being fetched from API
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
        <span className="ml-3 text-gray-600">Loading products...</span>
      </div>
    );
  }

  // Display error message if there was an API error
  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 text-lg">{error}</p>
        <p className="text-gray-500 mt-2">Please try searching again</p>
      </div>
    );
  }

  // Display "No products found" message if the 'products' prop array is empty
  // after loading is complete and no error occurred.
  // This accounts for both initial no results and results filtered to zero.
  if (!products || products.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-600 text-lg">
          {searchTerm
            ? `No products found for "${searchTerm}" with current filters.`
            : "No products found."}
        </p>
        <p className="text-gray-500 mt-2">
          Try adjusting your filters or search terms.
        </p>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-800">
          {searchTerm ? `Results for "${searchTerm}"` : "Products"} (
          {products.length} {products.length === 1 ? "item" : "items"})
        </h2>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        {/* Map over the 'products' prop (which will be the filteredProducts from parent) */}
        {products.map((product: Product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
};

export default ProductGrid;
