import React from "react";
import { Filter, ChevronDown } from "lucide-react";

const FilterSidebar: React.FC = () => {
  return (
    <div className="w-64 bg-white border-r border-gray-200 p-6 hidden lg:block">
      <div className="flex items-center mb-6">
        <Filter className="h-5 w-5 text-gray-500 mr-2" />
        <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
      </div>

      <div className="space-y-6">
        {/* Price Range */}
        <div>
          <button className="flex items-center justify-between w-full text-left">
            <span className="text-sm font-medium text-gray-900">
              Price Range
            </span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </button>
          <div className="mt-3 space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">$0 - $50</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">$50 - $100</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">$100 - $500</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">$500+</span>
            </label>
          </div>
        </div>

        {/* Supplier Type */}
        <div>
          <button className="flex items-center justify-between w-full text-left">
            <span className="text-sm font-medium text-gray-900">
              Supplier Type
            </span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </button>
          <div className="mt-3 space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">
                Verified Manufacturer
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">
                Trade Assurance
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">Gold Supplier</span>
            </label>
          </div>
        </div>

        {/* Rating */}
        <div>
          <button className="flex items-center justify-between w-full text-left">
            <span className="text-sm font-medium text-gray-900">Rating</span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </button>
          <div className="mt-3 space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">4.5+ Stars</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">4.0+ Stars</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">3.5+ Stars</span>
            </label>
          </div>
        </div>

        {/* Location */}
        <div>
          <button className="flex items-center justify-between w-full text-left">
            <span className="text-sm font-medium text-gray-900">Location</span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </button>
          <div className="mt-3 space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">China</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">India</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">United States</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterSidebar;
