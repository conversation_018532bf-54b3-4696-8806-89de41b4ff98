import React, { useState } from "react";

const SubscriptionContainer = () => {
  const [billingCycle, setBillingCycle] = useState("monthly");

  const plans = [
    {
      name: "Free",
      credits: 100,
      monthlyPrice: "$0",
      yearlyPrice: "$0",
      features: [
        "Basic web scraping",
        "100 credits per month",
        "Standard support",
        "Basic data export",
      ],
    },
    {
      name: "Pro",
      credits: 1000,
      monthlyPrice: "$29",
      yearlyPrice: "$299",
      features: [
        "Advanced web scraping",
        "1000 credits per month",
        "Priority support",
        "Advanced data export",
        "API access",
      ],
    },
    {
      name: "Enterprise",
      credits: 5000,
      monthlyPrice: "$99",
      yearlyPrice: "$999",
      features: [
        "Custom web scraping solutions",
        "5000 credits per month",
        "24/7 dedicated support",
        "Custom data export formats",
        "Full API access",
        "Custom integrations",
      ],
    },
  ];

  return (
    <div className="w-full min-h-screen py-16 px-4  flex items-center justify-center">
      <div className="max-w-7xl w-full mx-auto">
        {/* Header */}
        <div className="text-center mb-12 px-4">
          <h1 className="text-3xl md:text-5xl font-extrabold text-gray-900 mb-4 leading-tight">
            Choose your Plan
          </h1>
          <p className="text-md md:text-lg text-gray-600 mb-6">
            Unlock all features. Free 14-day trial. No credit card required.
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex p-1 bg-white rounded-full shadow-md">
            <button
              className={`px-4 md:px-6 py-2 rounded-full text-sm md:text-lg font-medium transition-colors duration-300 ${
                billingCycle === "monthly"
                  ? "bg-pink-400 text-white shadow-md"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
              onClick={() => setBillingCycle("monthly")}
            >
              Monthly
            </button>
            <button
              className={`px-4 md:px-6 py-2 rounded-full text-sm md:text-lg font-medium transition-colors duration-300 ${
                billingCycle === "yearly"
                  ? "bg-pink-400 text-white shadow-md"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
              onClick={() => setBillingCycle("yearly")}
            >
              Yearly
            </button>
          </div>
        </div>

        {/* Plan Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 pt-8">
          {plans.map((plan, index) => {
            const isPro = index === 1;
            return (
              <div
                key={plan.name}
                className={`relative rounded-2xl overflow-hidden shadow-lg flex flex-col transition-transform duration-300 ${
                  isPro
                    ? "bg-black scale-105 border-4 border-pink-400 z-10"
                    : "bg-white hover:scale-105"
                }`}
              >
                {isPro && (
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 bg-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold shadow-md z-20">
                    Most Popular
                  </div>
                )}

                <div className="px-6 md:px-8 py-8 flex-grow flex flex-col">
                  <h3
                    className={`text-xl md:text-3xl font-bold text-center mb-4 ${
                      isPro ? "text-white" : "text-gray-900"
                    }`}
                  >
                    {plan.name}
                  </h3>

                  <div className="text-center mb-6">
                    <span
                      className={`text-4xl md:text-5xl font-extrabold ${
                        isPro ? "text-pink-400" : "text-purple-600"
                      }`}
                    >
                      {billingCycle === "monthly"
                        ? plan.monthlyPrice
                        : plan.yearlyPrice}
                    </span>
                    <span
                      className={`text-sm md:text-xl ${
                        isPro ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      {billingCycle === "monthly" ? "/month" : "/year"}
                    </span>
                  </div>

                  <div className="text-center mb-6">
                    <span
                      className={`text-lg md:text-2xl font-semibold ${
                        isPro ? "text-white" : "text-gray-800"
                      }`}
                    >
                      {plan.credits}
                    </span>
                    <span
                      className={`ml-1 text-sm ${
                        isPro ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      credits
                    </span>
                  </div>

                  <ul className="space-y-4 mt-6 flex-grow">
                    {plan.features.map((feature) => (
                      <li
                        key={feature}
                        className="flex items-start md:items-center"
                      >
                        <svg
                          className={`h-5 w-5 md:h-6 md:w-6 mr-2 md:mr-3 flex-shrink-0 ${
                            isPro ? "text-pink-400" : "text-purple-500"
                          }`}
                          fill="none"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path d="M5 13l4 4L19 7" />
                        </svg>
                        <span
                          className={`text-sm md:text-lg ${
                            isPro ? "text-white" : "text-gray-700"
                          }`}
                        >
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="px-6 md:px-8 pb-8 mt-auto">
                  <button
                    className={`
                      w-full py-3 md:py-4 rounded-xl text-white text-lg md:text-xl font-semibold transition-all duration-300
                      ${
                        isPro
                          ? "bg-pink-500 hover:bg-pink-600"
                          : "bg-gradient-to-r from-purple-400 to-pink-300 hover:from-purple-600 hover:to-pink-600"
                      }
                    `}
                  >
                    Get Started
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionContainer;
