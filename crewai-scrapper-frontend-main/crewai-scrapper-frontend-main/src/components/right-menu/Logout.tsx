import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { logoutUser } from "../../api/services/Auth/AuthSlice";
import { RootState } from "../../api/store";
import { LogOut } from "lucide-react";
import { Button } from "@mui/material";

interface LogoutProps {
  onSignInClick?: () => void; // optional callback when user clicks Sign In
}

const Logout: React.FC<LogoutProps> = ({ onSignInClick }) => {
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );

  const handleLogout = () => {
    dispatch(logoutUser());
  };

  return (
    <>
      {isAuthenticated ? (
        <Button
          variant="outlined"
          startIcon={<LogOut size={18} />}
          color="error"
          size="small"
          onClick={handleLogout}
          disableRipple
          disableFocusRipple
          disableElevation
          sx={{
            textTransform: "none",
            transition: "none !important",
            "&:hover": {
              backgroundColor: "transparent !important",
              borderColor: "#f44336 !important",
            },
          }}
        >
          Logout
        </Button>
      ) : (
        <Button
          variant="contained"
          size="small"
          onClick={onSignInClick}
          sx={{
            backgroundColor: "#ec4899",
            color: "white",
            textTransform: "none",
            "&:hover": {
              backgroundColor: "#db2777",
            },
          }}
        >
          Sign In
        </Button>
      )}
    </>
  );
};

export default Logout;
