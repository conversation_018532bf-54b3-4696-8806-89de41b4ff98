import React from "react";
import {
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>on,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { X } from "lucide-react";

interface HelpCenterDrawerProps {
  open: boolean;
  onClose: () => void;
}

const faqData = [
  {
    question: "What is InvendoraAI?",
    answer:
      "InvendoraAI is your sourcing intelligence assistant, helping you discover suppliers, products, and insights tailored to your business.",
  },
  {
    question: "How can I access InvendoraAI features?",
    answer:
      "Sign up or log in to your InvendoraAI account. Once authenticated, you'll be able to explore the dashboard and tools.",
  },
  {
    question: "How do I report a bug or contact support?",
    answer:
      'Use the "Contact Support" option below or through your dashboard. Our team will respond promptly.',
  },
  {
    question: "Is there a free trial available?",
    answer:
      "Yes, we offer a 14-day free trial for all new users to explore InvendoraAI's full capabilities.",
  },
  {
    question: "Can I integrate InvendoraAI with my existing systems?",
    answer:
      "InvendoraAI offers various integration options through its API. Please refer to our documentation or contact support for details.",
  },
];

const HelpCenterDrawer: React.FC<HelpCenterDrawerProps> = ({
  open,
  onClose,
}) => {
  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <div className="w-[360px] h-full bg-white flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">Help Center</h2>
          <IconButton onClick={onClose}>
            <X size={20} className="text-gray-500 hover:text-gray-700" />
          </IconButton>
        </div>

        <div className="px-4 pt-3 pb-6 flex-1 overflow-y-auto">
          <Typography variant="h6" className="text-gray-800 font-semibold mb-3">
            Frequently Asked Questions
          </Typography>

          {faqData.map((item, index) => (
            <Accordion
              key={index}
              disableGutters
              elevation={0}
              sx={{
                boxShadow: "none",
                borderBottom: "1px solid #eee",
                "&:before": { display: "none" },
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon sx={{ color: "#4B5563" }} />}
                aria-controls={`faq-content-${index}`}
                id={`faq-header-${index}`}
              >
                <div className="flex items-center gap-2 text-sm text-gray-900 font-medium">
                  <span className="bg-gray-100 text-xs rounded p-1 px-2 text-gray-600 font-semibold">
                    Q
                  </span>
                  {item.question}
                </div>
              </AccordionSummary>
              <AccordionDetails>
                <div className="flex items-start gap-2 text-sm text-gray-700">
                  <span className="bg-green-100 text-xs rounded p-1 px-2 text-green-600 font-semibold">
                    A
                  </span>
                  <Typography variant="body2" className="text-sm text-gray-700">
                    {item.answer}
                  </Typography>
                </div>
              </AccordionDetails>
            </Accordion>
          ))}
        </div>
      </div>
    </Drawer>
  );
};

export default HelpCenterDrawer;
