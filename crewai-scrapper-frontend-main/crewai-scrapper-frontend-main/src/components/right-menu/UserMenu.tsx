import React, { useState } from "react";
import { Button } from "@mui/material";
import { HelpCircle, LogOut } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { logoutUser } from "../../api/services/Auth/AuthSlice";
import { RootState } from "../../api/store";
import { toast } from "react-toastify";
import HelpCenterDrawer from "./HelpCenterDrawer"; // Import the drawer component

interface UserMenuProps {
  onSignInClick?: () => void;
}

const UserMenu: React.FC<UserMenuProps> = ({ onSignInClick }) => {
  const [helpDrawerOpen, setHelpDrawerOpen] = useState(false);
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );

  const handleLogout = () => {
    dispatch(logoutUser());
    toast.success("Logged out successfully.");
  };

  const handleHelpCenterClick = () => {
    setHelpDrawerOpen(true);
  };

  const handleHelpDrawerClose = () => {
    setHelpDrawerOpen(false);
  };

  return (
    <>
      <div className="flex items-center justify-end gap-4">
        <Button
          startIcon={<HelpCircle size={18} />}
          variant="text"
          disableRipple
          disableFocusRipple
          disableElevation
          size="small"
          onClick={handleHelpCenterClick}
          sx={{
            textTransform: "none",
            color: "black",
            transition: "none !important",
            backgroundColor: "transparent !important",
            "&:hover": {
              backgroundColor: "transparent !important",
            },
          }}
        >
          Help Center
        </Button>

        {isAuthenticated ? (
          <Button
            variant="outlined"
            startIcon={<LogOut size={18} />}
            color="error"
            size="small"
            onClick={handleLogout}
            disableRipple
            disableFocusRipple
            disableElevation
            sx={{
              textTransform: "none",
              transition: "none !important",
              "&:hover": {
                backgroundColor: "transparent !important",
                borderColor: "#f44336 !important",
              },
            }}
          >
            Logout
          </Button>
        ) : (
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={onSignInClick}
            disableRipple
            disableFocusRipple
            disableElevation
            sx={{
              textTransform: "none",
              transition: "none !important",
              backgroundColor: "#ec4899 !important",
              "&:hover": {
                backgroundColor: "#db2777 !important",
              },
            }}
          >
            Sign In
          </Button>
        )}
      </div>

      {/* Help Center Drawer */}
      <HelpCenterDrawer
        open={helpDrawerOpen}
        onClose={handleHelpDrawerClose}
      />
    </>
  );
};

export default UserMenu;