import React, { useState, useEffect } from "react";
import { <PERSON>lide<PERSON> } from "@mui/material";
import {
  Minus,
  Plus,
  Star,
  Trash2,
  Filter as FilterIcon,
  DollarSign,
} from "lucide-react";
import { Product } from "../../types/Product";

interface ModernFilterSectionProps {
  allProducts: Product[];
  maxPriceLimit: number;
  onFiltered: (filtered: Product[]) => void;
  resetSignal?: number;
}

const ModernFilterSection: React.FC<ModernFilterSectionProps> = ({
  allProducts,
  maxPriceLimit,
  onFiltered,
  resetSignal,
}) => {
  const [tempPrice, setTempPrice] = useState<number[]>([0, maxPriceLimit]);
  const [tempRating, setTempRating] = useState<number>(0);
  const [tempMinQuantity, setTempMinQuantity] = useState<number>(1);
  const [filtersApplied, setFiltersApplied] = useState(false);

  // Sync max price with price range on change
  useEffect(() => {
    setTempPrice([0, maxPriceLimit]);
    setTempRating(0);
    setTempMinQuantity(1);
    setFiltersApplied(false);
    onFiltered(allProducts);
  }, [resetSignal, maxPriceLimit, allProducts, onFiltered]);

  // IMPORTANT: Always show all products initially (when no filters are applied)
  useEffect(() => {
    if (!filtersApplied) {
      onFiltered(allProducts);
    }
  }, [allProducts, filtersApplied, onFiltered]);

  const applyFilters = () => {
    const filtered = allProducts.filter((product: Product) => {
      const priceStr = product.price?.split("-")[0]?.replace(/[^0-9.]/g, "");
      const price = priceStr ? Number(priceStr) : 0;
      const rating = product.rating || 0;
      const qtyMatch = product.minimumOrderQuantity?.match(/\d+/);
      const quantity = qtyMatch ? Number(qtyMatch[0]) : 1;

      return (
        price >= tempPrice[0] &&
        price <= tempPrice[1] &&
        rating >= tempRating &&
        quantity >= tempMinQuantity
      );
    });

    setFiltersApplied(true);
    onFiltered(filtered);
  };

  const clearFilters = () => {
    setTempPrice([0, maxPriceLimit]);
    setTempRating(0);
    setTempMinQuantity(1);
    setFiltersApplied(false);
    // Show all products when clearing filters
    onFiltered(allProducts);
  };

  const incrementQty = () => setTempMinQuantity(tempMinQuantity + 1);
  const decrementQty = () =>
    setTempMinQuantity(Math.max(1, tempMinQuantity - 1));

  const hasChanges =
    tempPrice[0] > 0 ||
    tempPrice[1] < maxPriceLimit ||
    tempRating > 0 ||
    tempMinQuantity > 1;

  return (
    <div className="bg-white p-6 rounded-xl shadow mb-6 border border-gray-100">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Price Range */}
        <div className="flex flex-col">
          <label className="text-sm font-semibold text-gray-800 mb-2 flex items-center gap-1">
            <DollarSign size={14} /> Unit Price
          </label>
          <Slider
            value={tempPrice}
            onChange={(e, newValue) => setTempPrice(newValue as number[])}
            min={0}
            max={maxPriceLimit}
            step={0.1}
            valueLabelDisplay="off"
            sx={{
              color: "#ec4899",
              height: 4,
              maxWidth: 350,
              "& .MuiSlider-thumb": {
                height: 14,
                width: 14,
              },
            }}
          />
          <div className="flex justify-between text-sm text-gray-600 mt-1 max-w-[350px]">
            <span>${tempPrice[0]}</span>
            <span>${tempPrice[1]}</span>
          </div>
        </div>

        {/* Rating */}
        <div className="flex flex-col">
          <label className="text-sm font-semibold text-gray-800 mb-2 flex items-center gap-1">
            Rating <Star size={14} fill="#facc15" stroke="#facc15" />
          </label>
          <Slider
            value={tempRating}
            onChange={(e, value) => setTempRating(value as number)}
            min={0}
            max={5}
            step={0.5}
            marks
            valueLabelDisplay="off"
            sx={{
              color: "#facc15",
              height: 4,
              maxWidth: 350,
              "& .MuiSlider-thumb": {
                height: 14,
                width: 14,
              },
            }}
          />
          <div className="text-sm text-yellow-500 mt-1 flex items-center gap-1 max-w-[160px]">
            {tempRating === 0 ? "Any rating" : `${tempRating}+ stars`}{" "}
            <Star size={14} fill="#facc15" stroke="#facc15" />
          </div>
        </div>

        {/* Minimum Quantity */}
        <div className="flex flex-col">
          <label className="text-sm font-semibold text-gray-800 mb-2">
            Minimum Quantity
          </label>
          <div className="flex items-center border border-gray-300 rounded-full w-fit">
            <button
              onClick={decrementQty}
              className="px-3 py-1 text-gray-600 hover:text-pink-600"
            >
              <Minus size={16} />
            </button>
            <span className="px-4 text-sm font-medium text-gray-800">
              {tempMinQuantity}
            </span>
            <button
              onClick={incrementQty}
              className="px-3 py-1 text-gray-600 hover:text-pink-600"
            >
              <Plus size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* Filter Tags + Buttons */}
      <div className="flex justify-between items-center mt-6 flex-wrap gap-y-2">
        <div>
          <h3 className="text-sm font-semibold text-gray-800 mb-2">
            {filtersApplied ? "Applied Filters:" : "Filter Preview:"}
          </h3>

          {filtersApplied ? (
            <div className="space-x-2">
              {(tempPrice[0] > 0 || tempPrice[1] < maxPriceLimit) && (
                <span className="inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-sm font-medium">
                  ${tempPrice[0]} - ${tempPrice[1]}
                </span>
              )}
              {tempRating > 0 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 text-sm font-medium">
                  {tempRating}+ stars
                </span>
              )}
              {tempMinQuantity > 0 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
                  {tempMinQuantity}+ quantity
                </span>
              )}
              {tempPrice[0] === 0 &&
                tempPrice[1] === maxPriceLimit &&
                tempRating === 0 &&
                tempMinQuantity === 1 && (
                  <span className="text-sm text-gray-500 italic">
                    No filters applied.
                  </span>
                )}
            </div>
          ) : hasChanges ? (
            <div className="space-x-2 opacity-70">
              {(tempPrice[0] > 0 || tempPrice[1] < maxPriceLimit) && (
                <span className="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-600 text-sm font-medium">
                  ${tempPrice[0]} - ${tempPrice[1]}
                </span>
              )}
              {tempRating > 0 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-600 text-sm font-medium">
                  {tempRating}+ stars
                </span>
              )}
              {tempMinQuantity > 0 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-600 text-sm font-medium">
                  {tempMinQuantity}+ quantity
                </span>
              )}
            </div>
          ) : (
            <span className="text-sm text-gray-500 italic">
              No filters selected.
            </span>
          )}
        </div>

        {/* Buttons */}
        <div className="flex justify-end items-center gap-3 mt-2">
          {filtersApplied && (
            <button
              onClick={clearFilters}
              className="flex items-center gap-2 px-5 py-2 text-sm text-red-600 bg-red-100 hover:bg-red-200 rounded-md"
            >
              <Trash2 size={14} /> Clear Filters
            </button>
          )}

          <button
            onClick={applyFilters}
            disabled={!hasChanges}
            className={`flex items-center gap-2 px-5 py-2 text-sm rounded-md ${
              hasChanges
                ? "bg-pink-500 text-white hover:bg-pink-600"
                : "bg-gray-200 text-gray-400 cursor-not-allowed"
            }`}
          >
            <FilterIcon size={14} /> Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default ModernFilterSection;
