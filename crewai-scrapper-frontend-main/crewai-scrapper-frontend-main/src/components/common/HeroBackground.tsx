import React from "react";

const HeroBackground = () => {
  return (
    <div className="absolute -z-10 top-20  left-0 overflow-hidden w-full backdrop-blur-0">
      {/* SVG Design */}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1440 600"
        className="w-full h-full opacity-40"
        preserveAspectRatio="xMidYMid slice"
      >
        <defs>
          {/* Enhanced Gradients */}
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#ffe0e6" stopOpacity="0.9" />
            <stop offset="50%" stopColor="#ffc1d9" stopOpacity="0.6" />
            <stop offset="100%" stopColor="#ffb3d1" stopOpacity="0.4" />
          </linearGradient>

          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#ffe6ef" stopOpacity="0.8" />
            <stop offset="50%" stopColor="#ffd0e0" stopOpacity="0.5" />
            <stop offset="100%" stopColor="#ffb8d2" stopOpacity="0.3" />
          </linearGradient>

          <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#fff0f5" stopOpacity="0.7" />
            <stop offset="50%" stopColor="#ffd6e8" stopOpacity="0.5" />
            <stop offset="100%" stopColor="#ffccdc" stopOpacity="0.3" />
          </linearGradient>

          {/* Glow Filter */}
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* Enhanced Semi-Circles with Glow */}
        <g filter="url(#glow)">
          <path
            d="M450,300 A260,260 0 0 1 980,300"
            fill="none"
            stroke="url(#gradient1)"
            strokeWidth="40"
            opacity="0.6"
          />
          <path
            d="M490,300 A220,220 0 0 1 940,300"
            fill="none"
            stroke="url(#gradient2)"
            strokeWidth="38"
            opacity="0.5"
          />
          <path
            d="M530,300 A180,180 0 0 1 900,300"
            fill="none"
            stroke="url(#gradient3)"
            strokeWidth="36"
            opacity="0.4"
          />
        </g>
      </svg>
    </div>
  );
};

export default HeroBackground;
