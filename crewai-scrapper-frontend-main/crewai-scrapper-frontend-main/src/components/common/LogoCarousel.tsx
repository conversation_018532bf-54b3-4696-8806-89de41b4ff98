import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import { Autoplay } from "swiper/modules";

const logoTags = [
  { name: "Nike Supplier", logo: "/assets/logos/Nike-Logo-500x281.png" },
  { name: "Adidas Supplier", logo: "/assets/logos/Gap-Logo-500x333.png" }, // Replace with Adidas logo
  {
    name: "Under Armour Supplier",
    logo: "/assets/logos/Under-Armour-logo-500x315.png",
  },
  { name: "Levi's Supplier", logo: "/assets/logos/Levis-Logo-500x250.png" },
  {
    name: "<PERSON> Supplier",
    logo: "/assets/logos/<PERSON>-logo-500x281.png",
  },
  {
    name: "<PERSON> Supplier",
    logo: "/assets/logos/<PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-500x313.png",
  },
];

const duplicatedTags = [...logoTags, ...logoTags];

const LogoCarousel = () => {
  return (
    <div className="w-full px-2 sm:px-3 py-3 sm:py-4">
      <Swiper
        slidesPerView={2}
        spaceBetween={8}
        breakpoints={{
          480: {
            slidesPerView: 2.5,
            spaceBetween: 10,
          },
          640: {
            slidesPerView: 3,
            spaceBetween: 12,
          },
          768: {
            slidesPerView: 4,
            spaceBetween: 14,
          },
          1024: {
            slidesPerView: 5,
            spaceBetween: 16,
          },
          1280: {
            slidesPerView: 6,
            spaceBetween: 18,
          },
        }}
        autoplay={{
          delay: 2000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        loop
        modules={[Autoplay]}
        className="logo-swiper"
      >
        {duplicatedTags.map((item, index) => (
          <SwiperSlide key={`${item.name}-${index}`}>
            <div className="p-[1.5px] sm:p-[2px] rounded-full bg-gradient-to-r from-pink-400 via-fuchsia-300 to-yellow-200 hover:shadow-lg  transition-all duration-300 group">
              <div className="flex items-center  justify-center px-3 sm:px-4 lg:px-6 py-2.5 sm:py-3 h-12 sm:h-14 lg:h-16 rounded-full bg-white group-hover:bg-gray-50 transition-colors duration-300">
                <span className="text-xs sm:text-sm lg:text-base font-semibold text-gray-700 text-center leading-tight">
                  {item.name}
                </span>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default LogoCarousel;
