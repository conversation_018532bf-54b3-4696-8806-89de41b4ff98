import React, { useEffect, useState, useRef } from "react";
import {
  Eye,
  EyeOff,
  Mail,
  ArrowLeft,
  RefreshCw,
  User,
  Lock,
  CheckCircle,
  AlertCircle,
  X,
} from "lucide-react";
import {
  useRegisterMutation,
  useVerifyOtpMutation,
  useLoginMutation,
  useResendOtpMutation,
  useForgetPasswordMutation,
  useResetPasswordMutation,
} from "../../api/services/Auth/AuthService";
import { toast } from "react-toastify";
import { FcGoogle } from "react-icons/fc";
import { FaFacebook } from "react-icons/fa";
import { setCredentials } from "../../api/services/Auth/AuthSlice";
import { useDispatch } from "react-redux";

interface User {
  name: string;
  email: string;
}

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAuthSuccess?: (userData: { user: User; accessToken: string }) => void;
  step: "register" | "otp" | "login" | "forget-password" | "reset-password";
  setStep: React.Dispatch<
    React.SetStateAction<
      "register" | "otp" | "login" | "forget-password" | "reset-password"
    >
  >;
  defaultSignIn?: boolean;
}

const AuthModal: React.FC<AuthModalProps> = ({
  isOpen,
  onClose,
  step,
  setStep,
  defaultSignIn,
}) => {
  const dispatch = useDispatch();

  const [, setIsSignIn] = useState(defaultSignIn ?? true);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [otpError, setOtpError] = useState("");
  const [register, { isLoading: isRegistering }] = useRegisterMutation();
  const [verifyOtp, { isLoading: isVerifyingOtp }] = useVerifyOtpMutation();
  const [resendOtp] = useResendOtpMutation();
  const [login, { isLoading: isLoggingIn }] = useLoginMutation();
  const [registerErrorMsg, setRegisterErrorMsg] = useState("");
  const [loginErrorMsg, setLoginErrorMsg] = useState("");
  const [resendTimer, setResendTimer] = useState(20);
  const [canResend, setCanResend] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [isFormValid, setIsFormValid] = useState(false);
  const [resetPasswordOtp, setResetPasswordOtp] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);
  const [forgetPasswordEmail, setForgetPasswordEmail] = useState("");
  const [forgetPasswordError, setForgetPasswordError] = useState("");
  const [resetPasswordError, setResetPasswordError] = useState("");
  const [newPasswordStrength, setNewPasswordStrength] = useState(0);

  const [forgetPassword] = useForgetPasswordMutation();
  const [resetPassword] = useResetPasswordMutation();

  const otpInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Password strength calculation
  const calculatePasswordStrength = (pwd: string) => {
    let strength = 0;
    if (pwd.length >= 8) strength += 25;
    if (/[a-z]/.test(pwd)) strength += 25;
    if (/[A-Z]/.test(pwd)) strength += 25;
    if (/[0-9]/.test(pwd) && /[!@#$%^&*(),.?":{}|<>]/.test(pwd)) strength += 25;
    return strength;
  };

  // Form validation
  useEffect(() => {
    if (step === "register") {
      const isValid =
        name.trim().length >= 2 &&
        email.includes("@") &&
        password.length >= 8 &&
        password === confirmPassword;
      setIsFormValid(isValid);
    } else if (step === "login") {
      const isValid = email.includes("@") && password.length >= 6;
      setIsFormValid(isValid);
    }
  }, [name, email, password, confirmPassword, step]);

  // Reset form fields and errors when modal opens
  useEffect(() => {
    if (isOpen) {
      setShowPassword(false);
      setShowConfirmPassword(false);
      setEmail("");
      setName("");
      setPassword("");
      setConfirmPassword("");
      setOtp(["", "", "", "", "", ""]);
      setOtpError("");
      setRegisterErrorMsg("");
      setLoginErrorMsg("");
      setStep("login");
      setResendTimer(20);
      setCanResend(false);
      setPasswordStrength(0);
      setIsFormValid(false);
      setResetPasswordOtp("");
      setNewPassword("");
      setConfirmNewPassword("");
      setShowNewPassword(false);
      setShowConfirmNewPassword(false);
      setForgetPasswordEmail("");
      setForgetPasswordError("");
      setResetPasswordError("");
    }
  }, [isOpen, setStep]);

  // Reset form values when step changes
  useEffect(() => {
    setShowPassword(false);
    setShowConfirmPassword(false);
    setShowNewPassword(false);
    setShowConfirmNewPassword(false);
    setPassword("");
    setConfirmPassword("");
    setNewPassword("");
    setConfirmNewPassword("");
    setOtp(["", "", "", "", "", ""]);
    setOtpError("");
    setRegisterErrorMsg("");
    setLoginErrorMsg("");
    setResetPasswordError("");
    setResetPasswordOtp("");

    // Only reset email and name when switching to/from register
    if (step === "register") {
      setEmail("");
      setName("");
    }

    // Clear forget password email only when not moving to reset-password
    if (step !== "reset-password") {
      setForgetPasswordEmail("");
    }
    setForgetPasswordError("");
  }, [step]);

  // Password strength watchers
  useEffect(() => {
    setPasswordStrength(calculatePasswordStrength(password));
  }, [password]);

  useEffect(() => {
    setNewPasswordStrength(calculatePasswordStrength(newPassword));
  }, [newPassword]);

  // Timer for resend OTP
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (step === "otp" && resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [step, resendTimer]);

  useEffect(() => {
    setIsSignIn(defaultSignIn ?? true);
  }, [defaultSignIn]);

  if (!isOpen) return null;

  // Handle OTP input change
  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setOtpError("");

    if (value && index < 5) {
      otpInputRefs.current[index + 1]?.focus();
    }
  };

  // Handle OTP input keydown
  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      otpInputRefs.current[index - 1]?.focus();
    }
  };

  // Handle paste in OTP inputs
  const handleOtpPaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").slice(0, 6);
    const newOtp = [...otp];

    for (let i = 0; i < pastedData.length && i < 6; i++) {
      if (/^\d$/.test(pastedData[i])) {
        newOtp[i] = pastedData[i];
      }
    }

    setOtp(newOtp);
    setOtpError("");

    const nextIndex = Math.min(pastedData.length, 5);
    otpInputRefs.current[nextIndex]?.focus();
  };

  // Registration handler
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setRegisterErrorMsg("");
    try {
      await register({ name, email, password }).unwrap();
      toast.success("Registration successful! Please verify your email.");
      setStep("otp");
      setResendTimer(20);
      setCanResend(false);
    } catch (err) {
      let msg = "Registration failed. Please try again.";
      if (
        typeof err === "object" &&
        err !== null &&
        "data" in err &&
        typeof (err as { data?: { message?: string } }).data === "object" &&
        (err as { data?: { message?: string } }).data?.message
      ) {
        msg = (err as { data: { message: string } }).data.message;
      }
      setRegisterErrorMsg(msg);
      toast.error(msg);
    }
  };

  // OTP verification handler
  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setOtpError("");

    const otpString = otp.join("");
    if (otpString.length !== 6) {
      setOtpError("Please enter all 6 digits");
      return;
    }

    try {
      const res = await verifyOtp({ email, otp: otpString }).unwrap();
      const { user, accessToken } = res.data;
      dispatch(setCredentials({ user, accessToken }));
      toast.success("Email verified successfully! Welcome aboard! 🎉");
      onClose();
    } catch (err: unknown) {
      let msg = "Invalid verification code";
      if (
        typeof err === "object" &&
        err !== null &&
        "data" in err &&
        typeof (err as { data?: { message?: string } }).data === "object" &&
        (err as { data?: { message?: string } }).data?.message
      ) {
        msg = (err as { data: { message: string } }).data.message;
      }
      setOtpError(msg);
      toast.error(msg);
    }
  };

  // Resend OTP handler
  const handleResendOtp = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!canResend) return;

    try {
      const res = await resendOtp({ email }).unwrap();
      const { user, accessToken } = res.data;
      dispatch(setCredentials({ user, accessToken }));
      toast.success("Verification code sent! Check your email.");
      setResendTimer(20);
      setCanResend(false);
      setOtp(["", "", "", "", "", ""]);
      otpInputRefs.current[0]?.focus();
    } catch (err: unknown) {
      let msg = "Failed to resend code. Please try again.";
      if (
        typeof err === "object" &&
        err !== null &&
        "data" in err &&
        typeof (err as { data?: { message?: string } }).data === "object" &&
        (err as { data?: { message?: string } }).data?.message
      ) {
        msg = (err as { data: { message: string } }).data.message;
      }
      toast.error(msg);
    }
  };

  // Login handler
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginErrorMsg("");
    try {
      const resp = await login({ email, password }).unwrap();
      dispatch(setCredentials(resp.data));
      toast.success(`Welcome back! 👋`);
      onClose();
    } catch (err) {
      let msg = "Invalid email or password";
      if (
        typeof err === "object" &&
        err !== null &&
        "data" in err &&
        typeof (err as { data?: { message?: string } }).data === "object" &&
        (err as { data?: { message?: string } }).data?.message
      ) {
        msg = (err as { data: { message: string } }).data.message;
      }
      setLoginErrorMsg(msg);
      toast.error(msg);
    }
  };

  // Handle forget password
  const handleForgetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setForgetPasswordError("");
    try {
      await forgetPassword({ email: forgetPasswordEmail }).unwrap();
      toast.success("Password reset instructions sent to your email!");
      setStep("reset-password");
    } catch (err) {
      let msg = "Failed to process forget password request";
      if (
        typeof err === "object" &&
        err !== null &&
        "data" in err &&
        typeof (err as { data?: { message?: string } }).data === "object" &&
        (err as { data?: { message?: string } }).data?.message
      ) {
        msg = (err as { data: { message: string } }).data.message;
      }
      setForgetPasswordError(msg);
      toast.error(msg);
    }
  };

  // Handle reset password
  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setResetPasswordError("");

    if (newPassword !== confirmNewPassword) {
      setResetPasswordError("Passwords don't match");
      return;
    }

    try {
      const resp = await resetPassword({
        email: forgetPasswordEmail,
        otp: resetPasswordOtp,
        newPassword,
      }).unwrap();

      if (resp.success) {
        toast.success(
          "Password reset successful! Please login with your new password."
        );
        // Pre-fill the email in login form
        setEmail(forgetPasswordEmail);
        setStep("login");
      } else {
        throw new Error(resp.message || "Failed to reset password");
      }
    } catch (err) {
      let msg = "Failed to reset password";
      if (
        typeof err === "object" &&
        err !== null &&
        "data" in err &&
        typeof (err as { data?: { message?: string } }).data === "object" &&
        (err as { data?: { message?: string } }).data?.message
      ) {
        msg = (err as { data: { message: string } }).data.message;
      }
      setResetPasswordError(msg);
      toast.error(msg);
    }
  };

  const getPasswordStrengthColor = (strength = passwordStrength) => {
    if (strength < 25) return "bg-red-400";
    if (strength < 50) return "bg-orange-400";
    if (strength < 75) return "bg-yellow-400";
    return "bg-green-400";
  };

  const getPasswordStrengthText = (strength = passwordStrength) => {
    if (strength < 25) return "Weak";
    if (strength < 50) return "Fair";
    if (strength < 75) return "Good";
    return "Strong";
  };

  // Add Forget Password link in Login Form
  const renderLoginForm = () => (
    <form onSubmit={handleLogin} className="space-y-4 md:space-y-5">
      <div>
        <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
          Email Address
        </label>
        <div className="relative">
          <input
            type="email"
            placeholder="Enter your email address"
            className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          {email.includes("@") && (
            <CheckCircle
              className="absolute right-3 top-2.5 md:top-3 text-green-500"
              size={18}
            />
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
          Password
        </label>
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            placeholder="Enter your password"
            className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-2.5 md:top-3 text-gray-500 hover:text-gray-700 transition-colors"
          >
            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
      </div>

      {loginErrorMsg && (
        <div className="p-3 md:p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={16} />
            <p className="text-red-700 text-sm">{loginErrorMsg}</p>
          </div>
        </div>
      )}

      <div className="flex justify-end">
        <button
          type="button"
          onClick={() => setStep("forget-password")}
          className="text-sm text-purple-600 hover:text-purple-700 font-medium"
        >
          Forgot Password?
        </button>
      </div>

      <button
        type="submit"
        disabled={isLoggingIn || !isFormValid}
        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-2.5 md:py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm md:text-base"
      >
        {isLoggingIn ? (
          <span className="flex items-center justify-center">
            <RefreshCw size={16} className="animate-spin mr-2" />
            Signing In...
          </span>
        ) : (
          "Sign In"
        )}
      </button>

      <div className="text-center pt-3 md:pt-4">
        <p className="text-xs md:text-sm text-gray-600">
          Don't have an account?{" "}
          <button
            type="button"
            onClick={() => setStep("register")}
            className="text-purple-600 font-semibold hover:text-purple-700 transition-colors"
          >
            Register
          </button>
        </p>
      </div>
    </form>
  );

  // Add Forget Password Form
  const renderForgetPasswordForm = () => (
    <form onSubmit={handleForgetPassword} className="space-y-4 md:space-y-5">
      <div>
        <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
          Email Address
        </label>
        <div className="relative">
          <input
            type="email"
            placeholder="Enter your email address"
            className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
            required
            value={forgetPasswordEmail}
            onChange={(e) => setForgetPasswordEmail(e.target.value)}
          />
        </div>
      </div>

      {forgetPasswordError && (
        <div className="p-3 md:p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={16} />
            <p className="text-red-700 text-sm">{forgetPasswordError}</p>
          </div>
        </div>
      )}

      <button
        type="submit"
        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-2.5 md:py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200"
      >
        Send Reset Instructions
      </button>

      <div className="text-center pt-3 md:pt-4">
        <button
          type="button"
          onClick={() => setStep("login")}
          className="text-purple-600 font-semibold hover:text-purple-700 transition-colors text-sm"
        >
          Back to Login
        </button>
      </div>
    </form>
  );

  // Add Reset Password Form
  const renderResetPasswordForm = () => (
    <form onSubmit={handleResetPassword} className="space-y-4 md:space-y-5">
      <div>
        <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
          Email Address
        </label>
        <input
          type="email"
          value={forgetPasswordEmail}
          readOnly
          className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-4 border-2 border-gray-200 rounded-xl bg-gray-100 text-gray-600 text-sm md:text-base cursor-not-allowed"
        />
      </div>

      <div>
        <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
          Reset Code
        </label>
        <input
          type="text"
          placeholder="Enter the reset code from your email"
          className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
          required
          value={resetPasswordOtp}
          onChange={(e) => setResetPasswordOtp(e.target.value)}
        />
      </div>

      <div>
        <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
          New Password
        </label>
        <div className="relative">
          <input
            type={showNewPassword ? "text" : "password"}
            placeholder="Enter your new password"
            className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
            required
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
          />
          <button
            type="button"
            onClick={() => setShowNewPassword(!showNewPassword)}
            className="absolute right-3 top-2.5 md:top-3 text-gray-500 hover:text-gray-700 transition-colors"
          >
            {showNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
        {newPassword && (
          <div className="mt-2">
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-600">Password strength</span>
              <span
                className={`font-medium ${
                  newPasswordStrength >= 75
                    ? "text-green-600"
                    : newPasswordStrength >= 50
                    ? "text-yellow-600"
                    : "text-red-600"
                }`}
              >
                {getPasswordStrengthText(newPasswordStrength)}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div
                className={`h-1.5 rounded-full transition-all duration-300 ${getPasswordStrengthColor(
                  newPasswordStrength
                )}`}
                style={{ width: `${newPasswordStrength}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      <div>
        <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
          Confirm New Password
        </label>
        <div className="relative">
          <input
            type={showConfirmNewPassword ? "text" : "password"}
            placeholder="Confirm your new password"
            className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
            required
            value={confirmNewPassword}
            onChange={(e) => setConfirmNewPassword(e.target.value)}
          />
          <button
            type="button"
            onClick={() => setShowConfirmNewPassword(!showConfirmNewPassword)}
            className="absolute right-3 top-2.5 md:top-3 text-gray-500 hover:text-gray-700 transition-colors"
          >
            {showConfirmNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
          {confirmNewPassword && newPassword === confirmNewPassword && (
            <CheckCircle
              className="absolute right-10 top-2.5 md:top-3 text-green-500"
              size={18}
            />
          )}
        </div>
        {confirmNewPassword && newPassword !== confirmNewPassword && (
          <p className="text-red-500 text-xs mt-1">Passwords don't match</p>
        )}
      </div>

      {resetPasswordError && (
        <div className="p-3 md:p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={16} />
            <p className="text-red-700 text-sm">{resetPasswordError}</p>
          </div>
        </div>
      )}

      <button
        type="submit"
        disabled={
          !resetPasswordOtp ||
          !newPassword ||
          newPassword !== confirmNewPassword
        }
        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-2.5 md:py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Reset Password
      </button>

      <div className="text-center pt-3 md:pt-4">
        <button
          type="button"
          onClick={() => setStep("login")}
          className="text-purple-600 font-semibold hover:text-purple-700 transition-colors text-sm"
        >
          Back to Login
        </button>
      </div>
    </form>
  );

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-60 z-50 backdrop-blur-sm">
      <div className="bg-white rounded-3xl shadow-2xl flex flex-col md:flex-row w-[95%] max-w-5xl max-h-[90vh] md:min-h-[600px] overflow-y-auto md:overflow-hidden relative animate-in fade-in duration-300">
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-all duration-200 z-50 bg-white shadow-md"
          onClick={onClose}
        >
          <X size={20} />
        </button>

        {/* Left Panel */}
        <div className="hidden md:flex w-full md:w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 p-6 md:p-8 flex-col justify-center items-center text-center relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full"></div>
            <div className="absolute bottom-20 right-16 w-24 h-24 bg-white rounded-full"></div>
            <div className="absolute top-1/2 right-8 w-16 h-16 bg-white rounded-full"></div>
          </div>

          {/* Back button for OTP */}
          {(step === "otp" ||
            step === "forget-password" ||
            step === "reset-password") && (
            <button
              onClick={() => setStep(step === "otp" ? "register" : "login")}
              className="absolute top-6 left-6 flex items-center text-white/80 hover:text-white transition-colors bg-white/20 hover:bg-white/30 rounded-lg px-3 py-2"
            >
              <ArrowLeft size={18} className="mr-1" />
              Back
            </button>
          )}

          <div className="relative z-10 px-4">
            {step === "forget-password" ? (
              <div className="px-2">
                <div className="w-16 h-16 md:w-20 md:h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Mail size={28} className="text-white" />
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                  Forgot Password?
                </h2>
                <p className="text-white/90 text-base md:text-lg">
                  Enter your email to reset your password
                </p>
              </div>
            ) : step === "reset-password" ? (
              <div className="px-2">
                <div className="w-16 h-16 md:w-20 md:h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Lock size={28} className="text-white" />
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                  Reset Password
                </h2>
                <p className="text-white/90 text-base md:text-lg">
                  Enter the code and create a new password
                </p>
              </div>
            ) : step === "register" ? (
              <div className="px-2">
                <div className="w-16 h-16 md:w-20 md:h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 mx-auto">
                  <User size={28} className="text-white" />
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                  Join Our Community
                </h2>
                <p className="text-white/90 text-base md:text-lg">
                  Create your account and start your journey with us
                </p>
              </div>
            ) : (
              <div className="px-2">
                <div className="w-16 h-16 md:w-20 md:h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Lock size={28} className="text-white" />
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                  Welcome Back
                </h2>
                <p className="text-white/90 text-base md:text-lg">
                  Sign in to continue your journey
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel */}
        <div className="w-full md:w-1/2 pt-20 pb-6 md:pb-10 px-5 md:px-8 flex flex-col justify-center">
          {step !== "otp" &&
            step !== "forget-password" &&
            step !== "reset-password" && (
              <>
                {/* Social Login Buttons */}
                <div className="flex flex-col gap-3 mb-6">
                  <button
                    type="button"
                    className="flex items-center justify-center w-full py-2.5 md:py-3 rounded-xl border-2 border-gray-200 bg-white hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm hover:shadow-md group"
                  >
                    <FcGoogle className="w-5 h-5 mr-3" />
                    <span className="font-medium text-gray-700 group-hover:text-gray-900 text-sm md:text-base">
                      Continue with Google
                    </span>
                  </button>
                  <button
                    type="button"
                    className="flex items-center justify-center w-full py-2.5 md:py-3 rounded-xl border-2 border-[#1877f2] bg-[#1877f2] hover:bg-[#145db2] transition-all duration-200 text-white shadow-sm hover:shadow-md"
                  >
                    <FaFacebook className="w-5 h-5 mr-3" />
                    <span className="font-medium text-sm md:text-base">
                      Continue with Facebook
                    </span>
                  </button>
                </div>

                <div className="flex items-center justify-center my-4 md:my-6">
                  <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
                  <span className="mx-4 text-gray-500 bg-white px-2 text-sm">
                    or
                  </span>
                  <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
                </div>
              </>
            )}

          {step === "register" && (
            <form onSubmit={handleRegister} className="space-y-4 md:space-y-5">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
                  Full Name
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Enter your full name"
                    className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
                    required
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                  />
                  {name.length >= 2 && (
                    <CheckCircle
                      className="absolute right-3 top-2.5 md:top-3 text-green-500"
                      size={18}
                    />
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <input
                    type="email"
                    placeholder="Enter your email address"
                    className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                  {email.includes("@") && (
                    <CheckCircle
                      className="absolute right-3 top-2.5 md:top-3 text-green-500"
                      size={18}
                    />
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder="Create a strong password"
                    className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-2.5 md:top-3 text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {password && (
                  <div className="mt-2">
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-gray-600">Password strength</span>
                      <span
                        className={`font-medium ${
                          passwordStrength >= 75
                            ? "text-green-600"
                            : passwordStrength >= 50
                            ? "text-yellow-600"
                            : "text-red-600"
                        }`}
                      >
                        {getPasswordStrengthText(passwordStrength)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className={`h-1.5 rounded-full transition-all duration-300 ${getPasswordStrengthColor(
                          passwordStrength
                        )}`}
                        style={{ width: `${passwordStrength}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-1 md:mb-2">
                  Confirm Password
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    className="w-full p-2.5 md:p-3 pl-3 md:pl-4 pr-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-sm md:text-base"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-2.5 md:top-3 text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={18} />
                    ) : (
                      <Eye size={18} />
                    )}
                  </button>
                  {confirmPassword && password === confirmPassword && (
                    <CheckCircle
                      className="absolute right-10 top-2.5 md:top-3 text-green-500"
                      size={18}
                    />
                  )}
                </div>
                {confirmPassword && password !== confirmPassword && (
                  <p className="text-red-500 text-xs mt-1">
                    Passwords don't match
                  </p>
                )}
              </div>

              {registerErrorMsg && (
                <div className="p-3 md:p-4 bg-red-50 border border-red-200 rounded-xl">
                  <div className="flex items-center">
                    <AlertCircle className="text-red-500 mr-2" size={16} />
                    <p className="text-red-700 text-sm">{registerErrorMsg}</p>
                  </div>
                </div>
              )}

              <button
                type="submit"
                disabled={isRegistering || !isFormValid}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-2.5 md:py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm md:text-base"
              >
                {isRegistering ? (
                  <span className="flex items-center justify-center">
                    <RefreshCw size={16} className="animate-spin mr-2" />
                    Creating Account...
                  </span>
                ) : (
                  "Create Account"
                )}
              </button>

              <div className="text-center pt-3 md:pt-4">
                <p className="text-xs md:text-sm text-gray-600">
                  Already have an account?{" "}
                  <button
                    type="button"
                    onClick={() => setStep("login")}
                    className="text-purple-600 font-semibold hover:text-purple-700 transition-colors"
                  >
                    Sign In
                  </button>
                </p>
              </div>
            </form>
          )}

          {step === "otp" && (
            <div className="pt-2 md:pt-4">
              <form
                onSubmit={handleVerifyOtp}
                className="space-y-4 md:space-y-6"
              >
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3 md:mb-4 text-center">
                    Enter 6-Digit Verification Code
                  </label>

                  <div className="flex justify-center gap-2 md:gap-3 mb-4 md:mb-6">
                    {otp.map((digit, index) => (
                      <input
                        key={index}
                        ref={(el: HTMLInputElement | null) => {
                          otpInputRefs.current[index] = el;
                        }}
                        type="text"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        maxLength={1}
                        value={digit}
                        onChange={(e) => handleOtpChange(index, e.target.value)}
                        onKeyDown={(e) => handleOtpKeyDown(index, e)}
                        onPaste={index === 0 ? handleOtpPaste : undefined}
                        className={`w-10 h-10 md:w-12 md:h-12 text-center text-lg md:text-xl font-bold border-2 rounded-xl focus:outline-none transition-all duration-200 ${
                          otpError
                            ? "border-red-300 focus:border-red-500 bg-red-50"
                            : digit
                            ? "border-purple-400 bg-purple-50 text-purple-700"
                            : "border-gray-300 focus:border-purple-500 bg-white"
                        } shadow-sm focus:shadow-md`}
                      />
                    ))}
                  </div>

                  {otpError && (
                    <div className="mb-3 md:mb-4 p-3 md:p-4 bg-red-50 border border-red-200 rounded-xl">
                      <div className="flex items-center justify-center">
                        <AlertCircle className="text-red-500 mr-2" size={16} />
                        <p className="text-red-700 text-sm">{otpError}</p>
                      </div>
                    </div>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={isVerifyingOtp || otp.join("").length !== 6}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-2.5 md:py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-sm md:text-base"
                >
                  {isVerifyingOtp ? (
                    <span className="flex items-center justify-center">
                      <RefreshCw size={16} className="animate-spin mr-2" />
                      Verifying Code...
                    </span>
                  ) : (
                    "Verify Email"
                  )}
                </button>

                <div className="text-center space-y-3 md:space-y-4">
                  <p className="text-xs md:text-sm text-gray-600">
                    Didn't receive the code?
                  </p>
                  {canResend ? (
                    <button
                      type="button"
                      onClick={handleResendOtp}
                      className="text-purple-600 font-semibold hover:text-purple-700 text-xs md:text-sm flex items-center justify-center mx-auto bg-purple-50 hover:bg-purple-100 px-3 py-1.5 md:px-4 md:py-2 rounded-lg transition-all duration-200"
                    >
                      <RefreshCw size={14} className="mr-2" />
                      Resend Code
                    </button>
                  ) : (
                    <div className="bg-gray-100 px-3 py-1.5 md:px-4 md:py-2 rounded-lg inline-block">
                      <p className="text-xs md:text-sm text-gray-600">
                        Resend available in{" "}
                        <span className="font-semibold text-purple-600">
                          {resendTimer}s
                        </span>
                      </p>
                    </div>
                  )}
                </div>

                <div className="text-center pt-3 md:pt-4 border-t border-gray-200">
                  <p className="text-xs md:text-sm text-gray-600">
                    Wrong email?{" "}
                    <button
                      type="button"
                      onClick={() => setStep("register")}
                      className="text-purple-600 font-semibold hover:text-purple-700 transition-colors"
                    >
                      Change Email
                    </button>
                  </p>
                </div>
              </form>
            </div>
          )}

          {step === "login" && renderLoginForm()}

          {step === "forget-password" && renderForgetPasswordForm()}

          {step === "reset-password" && renderResetPasswordForm()}
        </div>
      </div>
    </div>
  );
};

export default AuthModal;
