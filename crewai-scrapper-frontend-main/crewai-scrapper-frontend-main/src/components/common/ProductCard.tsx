import { SearchResult } from "../../types";

interface ProductCardProps {
  product: SearchResult;
}

const ProductCard = ({ product }: ProductCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      {product.imageUrl && (
        <img
          src={product.imageUrl}
          alt={product.title}
          className="w-full h-48 object-cover"
        />
      )}
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
          {product.title}
        </h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {product.description}
        </p>
        {product.price && (
          <p className="text-lg font-bold text-[#f545d4] mb-4">
            {product.price}
          </p>
        )}
      </div>
    </div>
  );
};

export default ProductCard;
