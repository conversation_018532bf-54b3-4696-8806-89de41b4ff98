import React, { useEffect, useState } from "react";
import ProductCard from "../common/ProductCard";
import { SearchResult } from "../../types";
import axios from "axios";

interface Props {
  query: string;
  setScrapingButtonLoading: (val: boolean) => void;
  scrapingButtonLoading: boolean;
  setIsScraped: (val: boolean) => void;
}

const SearchResults: React.FC<Props> = ({
  query,
  setScrapingButtonLoading,
  scrapingButtonLoading,
  setIsScraped,
}) => {
  const [results, setResults] = useState<SearchResult[]>([
    {
      id: "1",
      title: "Test",
      description: "Test",
      price: "100",
      imageUrl: "https://via.placeholder.com/150",
      url: "https://www.google.com",
    },
  ]);

  useEffect(() => {
    const fetchData = async () => {
      setScrapingButtonLoading(true);
      try {
        const response = await axios.post(
          `${import.meta.env.VITE_BASE_URL}/scraper/alibaba/scrape`,
          { query }
        );
        const apiData = response?.data?.data?.data;

        if (apiData) {
          const product: SearchResult = {
            id: "1",
            title: apiData?.title || "No title available",
            description: apiData?.description || "No description available",
            price: apiData?.price || "Contact for price",
            imageUrl:
              apiData?.images?.find(
                (img: { url: string }) =>
                  img?.url?.includes(".jpg") || img?.url?.includes(".png")
              )?.url ||
              "https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg",
            url: response?.data?.data?.url || "#",
          };

          setResults([product]);
          setIsScraped(true);
        } else {
          setIsScraped(false);
        }
      } catch (error) {
        console.error("Error fetching search results:", error);
        setIsScraped(false);
      } finally {
        setScrapingButtonLoading(false);
      }
    };

    if (query) {
      fetchData();
    }
  }, [query]);

  return scrapingButtonLoading ? (
    "Loading..."
  ) : (
    <div className="mx-6 px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Search Results</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {results.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
};

export default SearchResults;
