import {
  Search,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
} from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-gray-900 to-purple-900 text-white relative overflow-hidden">
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center">
              <Search className="h-8 w-8 text-yellow-400 mr-2" />
              <span className="text-2xl font-bold">Invendora</span>
            </div>
            <p className="text-gray-300 leading-relaxed">
              The world's leading vendor marketplace connecting businesses with
              verified professionals worldwide.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="p-2 bg-white/10 rounded-lg text-gray-300 hover:text-purple-400 hover:bg-white/20 transition-all duration-300"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="p-2 bg-white/10 rounded-lg text-gray-300 hover:text-pink-400 hover:bg-white/20 transition-all duration-300"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="p-2 bg-white/10 rounded-lg text-gray-300 hover:text-red-400 hover:bg-white/20 transition-all duration-300"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="p-2 bg-white/10 rounded-lg text-gray-300 hover:text-yellow-400 hover:bg-white/20 transition-all duration-300"
              >
                <Instagram className="h-5 w-5" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              For Businesses
            </h3>
            <ul className="space-y-3 text-gray-300">
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Find Vendors
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Post a Project
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Vendor Categories
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Success Stories
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Pricing
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">
              For Vendors
            </h3>
            <ul className="space-y-3 text-gray-300">
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Join as Vendor
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Vendor Resources
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Success Tips
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Community
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white transition-colors hover:translate-x-1 transform duration-200 inline-block"
                >
                  Vendor Support
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 bg-gradient-to-r from-[#F545D4] via-[#BA68C8] to-[#F0BD57] bg-clip-text text-transparent">
              Contact Info
            </h3>
            <div className="space-y-4 text-gray-300">
              <div className="flex items-center group">
                <div className="p-2 bg-purple-500/20 rounded-lg mr-3 group-hover:bg-purple-500/30 transition-colors">
                  <Mail className="h-4 w-4 text-purple-400" />
                </div>
                <span><EMAIL></span>
              </div>
              <div className="flex items-center group">
                <div className="p-2 bg-pink-500/20 rounded-lg mr-3 group-hover:bg-pink-500/30 transition-colors">
                  <Phone className="h-4 w-4 text-pink-400" />
                </div>
                <span>+****************</span>
              </div>
              <div className="flex items-center group">
                <div className="p-2 bg-yellow-500/20 rounded-lg mr-3 group-hover:bg-yellow-500/30 transition-colors">
                  <MapPin className="h-4 w-4 text-yellow-400" />
                </div>
                <span>San Francisco, CA</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-white/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2025 Invendora. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a
                href="#"
                className="text-gray-400 hover:text-white text-sm transition-colors"
              >
                Privacy Policy
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white text-sm transition-colors"
              >
                Terms of Service
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white text-sm transition-colors"
              >
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
