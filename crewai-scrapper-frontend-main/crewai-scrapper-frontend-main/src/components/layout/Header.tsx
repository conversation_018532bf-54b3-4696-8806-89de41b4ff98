import React, { useState } from "react";
import { useSelector } from "react-redux";
import { User } from "lucide-react";
import { RootState } from "../../api/store";
import { Link } from "react-router-dom";
import AuthModal from "../common/AuthModal";

const Header = () => {
  const [step, setStep] = useState<"login" | "register" | "otp">("login");

  const { user } = useSelector((state: RootState) => state.auth);
  const isAuthenticated = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );

  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [defaultSignIn, setDefaultSignIn] = useState(true);

  return (
    <>
      <header className="absolute top-0 left-0 right-0 z-50 bg-transparent">
        <div className="mx-auto px-3 sm:px-4 lg:px-6 py-2 sm:py-3">
          <div className="flex justify-between items-center h-12 sm:h-14 lg:h-16">
            {/* Logo */}
            <Link
              to="/home"
              className="flex items-center p-2 sm:p-3 rounded-full bg-white/20 backdrop-blur-sm shadow-md hover:bg-white/30 transition-all duration-300"
            >
              <img
                src="/assets/small-logo.png"
                alt="Logo"
                className="h-6 sm:h-6 max-w-[100px] sm:max-w-none"
              />
              <img
                src="/assets/large-logo.svg"
                alt="Logo Text"
                className="h-6 sm:h-6 max-w-[100px] sm:max-w-none"
              />
            </Link>

            {/* Right-side Menu */}
            {isAuthenticated ? (
              <div className="flex items-center gap-2 sm:gap-3 lg:gap-4 px-2 sm:px-3 py-1.5 sm:py-2 rounded-full bg-white/20 backdrop-blur-sm shadow-lg">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium shadow-md">
                  <span className="hidden sm:inline">
                    {user?.name || "User"}
                  </span>
                  <span className="sm:hidden">
                    {(user?.name || "User").charAt(0)}
                  </span>
                </div>
                <Link to="/home">
                  <div className="p-1.5 sm:p-2 hover:bg-white/30 rounded-full transition-all duration-300">
                    <User className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-gray-700" />
                  </div>
                </Link>
              </div>
            ) : (
              <div className="flex">
                <button
                  onClick={() => {
                    setDefaultSignIn(false);
                    setIsAuthModalOpen(true);
                  }}
                  className="px-3 sm:px-4 lg:px-6 py-2 sm:py-2.5 lg:py-3 text-xs sm:text-sm lg:text-base 
                    bg-gradient-to-r from-[#f545d4] to-[#fddea2] 
                    text-white rounded-full hover:opacity-90 
                    transition-all duration-300 shadow-md font-medium 
                    backdrop-blur-sm whitespace-nowrap"
                >
                  Sign In
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Auth Modal */}
      <AuthModal
        step={step}
        setStep={setStep}
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        defaultSignIn={defaultSignIn}
        onAuthSuccess={() => setIsAuthModalOpen(false)}
      />
    </>
  );
};

export default Header;
