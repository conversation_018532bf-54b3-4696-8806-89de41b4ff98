import { useState } from "react";
import Sidebar from "./Sidebar";
import AuthModal from "../../components/common/AuthModal";
import { Outlet } from "react-router-dom";

const AppLayout = () => {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authStep, setAuthStep] = useState<"register" | "otp" | "login">(
    "login"
  );

  return (
    <div className="flex bg-gradient-to-br from-white to-pink-50 overflow-hidden h-screen">
      <Sidebar />

      <div className="flex-1 flex flex-col min-h-screen">
        <Outlet />
      </div>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        step={authStep}
        setStep={setAuthStep}
      />
    </div>
  );
};

export default AppLayout;
