import { Outlet, useLocation } from "react-router-dom";
import Header from "./Header";
import Footer from "./Footer";

const MainLayout = () => {
  const location = useLocation();
  const authRoutes = ["/login", "/signup", "/forgot-password"];
  const showHeader = !authRoutes.includes(location.pathname);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {showHeader && <Header />}
      <main className="flex-grow">
        <Outlet />
      </main>
      {showHeader && <Footer />}
    </div>
  );
};

export default MainLayout;
