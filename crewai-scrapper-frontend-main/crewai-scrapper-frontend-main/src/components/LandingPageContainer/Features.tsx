import { Shield, Clock, Globe, Brain, Bell, TrendingUp } from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Precision",
      description:
        "Our advanced AI algorithms instantly surface the best-fit suppliers—no more endless keyword searches or sifting through outdated lists.",
      image:
        "https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg?auto=compress&cs=tinysrgb&w=600",
      gradient: "from-purple-600 to-pink-500",
    },
    {
      icon: Shield,
      title: "Curated & Verified Partners",
      description:
        "Every supplier in our database is hand-vetted for quality, reliability, and legitimacy—so you can confidently source from brands like Nike, Victoria's Secret, and beyond.",
      image:
        "https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg?auto=compress&cs=tinysrgb&w=600",
      gradient: "from-pink-500 to-red-500",
    },
    {
      icon: Globe,
      title: "Global Reach, Local Insight",
      description:
        "From Los Angeles to Shenzhen, Inventora covers key manufacturing hubs worldwide, with localized data on minimum order quantities, lead times, and compliance.",
      image:
        "https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&w=600",
      gradient: "from-red-500 to-orange-400",
    },
    {
      icon: Clock,
      title: "Time & Cost Efficiency",
      description:
        "Spend less time on research and more time scaling—our platform cuts your supplier-hunt from days to minutes, helping you hit the ground running without breaking the bank.",
      image:
        "https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800",
      gradient: "from-orange-400 to-yellow-300",
    },
    {
      icon: Bell,
      title: "Real-Time Updates & Alerts",
      description:
        "Get notified about new suppliers, price changes, or MOQ adjustments the moment they happen—so you're always ahead of market shifts.",
      image:
        "https://images.pexels.com/photos/3184454/pexels-photo-3184454.jpeg?auto=compress&cs=tinysrgb&w=600",
      gradient: "from-purple-500 to-orange-400",
    },
    {
      icon: TrendingUp,
      title: "Scalable for Every Stage",
      description:
        "Whether you're launching your first product or expanding a global line, Inventora's flexible plans and insights grow with your business—no hidden fees, no surprises.",
      image:
        "https://images.pexels.com/photos/4386431/pexels-photo-4386431.jpeg?auto=compress&cs=tinysrgb&w=600",
      gradient: "from-pink-400 to-yellow-300",
    },
  ];

  return (
    <section id="features" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold bg-gradient-to-r from-[#F545D4] via-[#BA68C8] to-[#F0BD57] bg-clip-text text-transparent mb-4">
            Why Choose Invendora?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We've built the most comprehensive vendor marketplace with features
            designed to make your vendor search and management experience
            seamless.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 group border border-gray-100"
            >
              <div className="h-48 overflow-hidden relative">
                <img
                  src={feature.image}
                  alt={feature.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div
                  className={`absolute inset-0 bg-gradient-to-t ${feature.gradient} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}
                ></div>
              </div>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div
                    className={`bg-gradient-to-r ${feature.gradient} p-3 rounded-xl mr-4 shadow-lg`}
                  >
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
