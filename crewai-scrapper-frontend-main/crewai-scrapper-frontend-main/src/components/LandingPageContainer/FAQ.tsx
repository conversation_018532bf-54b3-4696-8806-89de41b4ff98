import { useState } from "react";
import { ChevronDown, ChevronUp, HelpCircle } from "lucide-react";

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "What is Invendora?",
      answer:
        "Invendora is an AI-powered B2B search engine that helps entrepreneurs and small businesses quickly find and connect with verified wholesale suppliers across fashion, beauty, stationery, and more.",
    },
    {
      question: "How does Invendora work?",
      answer:
        "Simply type in what you're looking for—whether it's “women’s hair extensions” or “custom notebooks”—and our AI will surface a curated list of global suppliers complete with MOQs, contact info, and key product details.",
    },
    {
      question: "What product categories do you support?",
      answer:
        "We currently cover: Apparel & Accessories, Hair & Beauty, Stationery & Office Supplies, Home & Lifestyle. New categories are added weekly!",
    },
    {
      question: "Is there a free trial?",
      answer:
        "Yes! Our free tier gives you 5 searches/month with access to basic supplier profiles. No credit card required—just sign up and start exploring.",
    },
    {
      question: "How do you process payments?",
      answer:
        "We use Stripe, a leading secure payment processor, to handle all credit card and PayPal transactions. <PERSON><PERSON> is PCI-compliant and ensures your payment data is encrypted end-to-end.",
    },
  ];

  return (
    <section id="faq" className="py-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <HelpCircle className="h-12 w-12 text-pink-600 mr-3" />
            <h2 className="text-4xl font-bold bg-gradient-to-r from-[#F545D4] via-[#BA68C8] to-[#F0BD57] bg-clip-text text-transparent">
              Frequently Asked Questions
            </h2>
          </div>
          <p className="text-xl text-gray-600">
            Everything you need to know about finding and working with vendors
            on Invendora.
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 overflow-hidden hover:shadow-xl transition-all duration-300"
            >
              <button
                className="w-full px-8 py-6 text-left focus:outline-none focus:ring-inset"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 pr-4">
                    {faq.question}
                  </h3>
                  <div
                    className={`p-2 rounded-full transition-all duration-300 ${
                      openIndex === index
                        ? "bg-gradient-to-r from-[#F545D4] via-[#BA68C8] to-[#F0BD57]"
                        : "bg-gray-100"
                    }`}
                  >
                    {openIndex === index ? (
                      <ChevronUp className="h-5 w-5 text-white flex-shrink-0" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" />
                    )}
                  </div>
                </div>
              </button>
              {openIndex === index && (
                <div className="px-8 pb-6">
                  <div className="w-full h-px bg-gradient-to-r from-purple-200 via-pink-200 to-yellow-200 mb-4"></div>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
