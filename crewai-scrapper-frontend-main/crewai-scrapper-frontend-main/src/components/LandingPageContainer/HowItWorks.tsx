import { Sliders, Database, Filter, PhoneCall } from "lucide-react";

const HowItWorks = () => {
  const steps = [
    {
      icon: Sliders,
      title: "Set Your Wholesale Parameters",
      description:
        "Enter in what wholesale items you're looking for and define your priorities like price, lead time, or certifications.",
      image:
        "https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=800",
      gradient: "from-purple-600 to-pink-500",
    },
    {
      icon: Database,
      title: "AI-Enriched Vendor Database",
      description:
        "Our AI continuously crawls, verifies, and enriches data on top wholesale vendors and manufacturers worldwide.",
      image:
        "https://images.pexels.com/photos/3184433/pexels-photo-3184433.jpeg?auto=compress&cs=tinysrgb&w=800",
      gradient: "from-pink-500 to-red-500",
    },
    {
      icon: Filter,
      title: "Smart Filters & Vendor Rankings",
      description:
        "Use smart filters to drill down by pricing, lead times, certifications, and ratings—AI ranks vendors based on your needs.",
      image:
        "https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800",
      gradient: "from-red-500 to-orange-400",
    },
    {
      icon: PhoneCall,
      title: "Direct Contact Details",
      description:
        "Instantly access full vendor profiles including websites, emails, phone numbers, and social links to connect directly.",
      image:
        "https://images.pexels.com/photos/3184357/pexels-photo-3184357.jpeg?auto=compress&cs=tinysrgb&w=800",
      gradient: "from-orange-400 to-yellow-300",
    },
  ];

  return (
    <section id="how-it-works" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold g-gradient-to-r from-[#F545D4] via-[#BA68C8] to-[#F0BD57]  bg-clip-text text-transparent mb-4">
            How Invendora Works
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Finding the right vendor has never been easier. Follow these simple
            steps to connect with verified professionals who can help grow your
            business.
          </p>
        </div>

        <div className="space-y-20">
          {steps.map((step, index) => (
            <div
              key={index}
              className={`flex flex-col ${
                index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"
              } items-center gap-12`}
            >
              <div className="flex-1">
                <div className="relative group">
                  <img
                    src={step.image}
                    alt={step.title}
                    className="w-full h-80 object-cover rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500"
                  />
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${step.gradient} opacity-20 rounded-3xl group-hover:opacity-30 transition-opacity duration-300`}
                  ></div>
                  <div
                    className={`absolute -top-6 -left-6 bg-gradient-to-r ${step.gradient} text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold shadow-2xl`}
                  >
                    {index + 1}
                  </div>
                </div>
              </div>
              <div className="flex-1 space-y-6">
                <div className="flex items-center space-x-4">
                  <div
                    className={`bg-gradient-to-r ${step.gradient} p-4 rounded-2xl shadow-lg`}
                  >
                    <step.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900">
                    {step.title}
                  </h3>
                </div>
                <p className="text-lg text-gray-600 leading-relaxed">
                  {step.description}
                </p>
                {index < steps.length - 1 && (
                  <div className="flex items-center space-x-3 text-pink-600">
                    <span className="font-medium">Next step</span>
                    <div className="w-12 h-1 bg-gradient-to-r from-pink-400 to-yellow-400 rounded-full"></div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
