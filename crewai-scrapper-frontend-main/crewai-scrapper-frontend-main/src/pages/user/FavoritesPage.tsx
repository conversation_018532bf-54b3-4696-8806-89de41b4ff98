import React, { useState, useEffect } from "react";
import { Product } from "../../types/Product";
import ProductCard from "../../components/product/ProductCard";
import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { useGetFavouritesQuery } from "../../api/services/Favourites/FavouritesService";

const ITEMS_PER_PAGE = 10;

const FavoritesPage: React.FC = () => {
  const { data, isLoading, isError } = useGetFavouritesQuery(undefined);
  const allFavoriteProducts: Product[] = data?.data?.favourites ?? [];

  const [visibleCount, setVisibleCount] = useState(ITEMS_PER_PAGE);
  const [displayedProducts, setDisplayedProducts] = useState<Product[]>([]);

  const navigate = useNavigate();

  useEffect(() => {
    if (allFavoriteProducts.length > 0) {
      setDisplayedProducts(allFavoriteProducts.slice(0, visibleCount));
    }
  }, [allFavoriteProducts, visibleCount]);

  const handleLoadMore = () => {
    setVisibleCount((prev) => prev + ITEMS_PER_PAGE);
  };

  const handleBackToProducts = () => {
    navigate("/products");
  };

  return (
    <div className="px-6 py-8 min-h-screen overflow-y-scroll">
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={handleBackToProducts}
          className="flex items-center px-3 py-1 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-colors duration-200"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Products
        </button>
      </div>

      <h1 className="text-2xl font-bold mb-6 text-pink-600">
        Your Favorite Products
      </h1>

      {isLoading ? (
        <div className="text-center text-gray-500 py-12">
          Loading your favorites...
        </div>
      ) : isError ? (
        <div className="text-center text-red-500 py-12">
          Failed to load favorites. Please try again.
        </div>
      ) : allFavoriteProducts.length > 0 ? (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
            {displayedProducts.map((product: Product) => (
              <ProductCard
                key={product.id}
                product={product}
                isInFavoritePage={true}
              />
            ))}
          </div>

          {visibleCount < allFavoriteProducts.length && (
            <div className="flex justify-center mt-6">
              <button
                onClick={handleLoadMore}
                className="px-6 py-2 bg-pink-500 text-white rounded-lg shadow hover:bg-pink-600 transition"
              >
                Load More
              </button>
            </div>
          )}
        </>
      ) : (
        <div className="text-center text-gray-500 py-12">
          You haven't added any products to favorites yet.
        </div>
      )}
    </div>
  );
};

export default FavoritesPage;
