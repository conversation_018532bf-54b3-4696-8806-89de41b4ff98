import React, { useState } from "react";
import { BarChart, Box, Factory, Search } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import UserMenu from "../../components/right-menu/UserMenu";
import AuthModal from "../../components/common/AuthModal";
import { ChatPageStaticData } from "../../data/ChatPageStaticData";
import { saveSearchToHistory } from "../../utils/searchHistory";

const ChatPage: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [step, setStep] = useState<
    "register" | "otp" | "login" | "forget-password" | "reset-password"
  >("login");
  const [searchTerm, setSearchTerm] = useState("");

  const navigate = useNavigate();

  const handleSearch = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      // Save the original search term to history
      saveSearchToHistory(searchTerm);

      // Navigate with the original search term
      navigate(`/products?search=${encodeURIComponent(searchTerm)}`);
      setSearchTerm("");
    }
  };

  const closeModal = () => setModalOpen(false);

  const iconMapping = {
    Box: Box,
    Factory: Factory,
    BarChart: BarChart,
  };

  return (
    <>
      <div className="min-h-screen w-full flex flex-col items-center bg-white pt-4 px-4 sm:px-6 lg:px-12 overflow-y-auto">
        {/* Top-right user menu */}
        <div className="w-full flex justify-end mb-4 sm:mb-6">
          <UserMenu onSignInClick={() => setModalOpen(true)} />
        </div>

        {/* Logo and description */}
        <div className="text-center mb-10 sm:mb-16 mt-20 sm:mt-28">
          <div className="flex items-center justify-center mb-4">
            <img
              src="/assets/large-logo.svg"
              alt="Large Logo"
              className="h-8 sm:h-10 w-auto"
            />
          </div>
          <p className="text-xl sm:text-2xl text-gray-600">
            Find suppliers and wholesalers for your business using Ai
          </p>
        </div>

        {/* Search bar */}
        <div className="w-full max-w-2xl px-2 sm:px-4 mb-10">
          <form onSubmit={handleSearch} className="relative w-full">
            <div className="bg-gradient-to-r from-pink-400 via-fuchsia-300 to-yellow-200 p-[2px] rounded-2xl shadow-md">
              <input
                type="text"
                className="w-full text-sm sm:text-base md:text-lg px-4 sm:px-6 pr-[100px] py-3 sm:py-4 bg-white rounded-[1rem] focus:outline-none focus:ring-4 focus:ring-pink-100 transition-all duration-300"
                placeholder="What wholesale vendors are you looking for..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Search Button */}
            <button
              type="submit"
              disabled={!searchTerm.trim()}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-gradient-to-br from-pink-500 to-pink-300 text-white rounded-xl px-4 sm:px-6 py-2 text-sm sm:text-base hover:from-pink-600 hover:to-pink-400 transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <Search className="w-4 h-4 mr-2" />
              Search
            </button>
          </form>
        </div>

        {/* Static category grid */}
        <div className="w-full max-w-6xl grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 sm:gap-6 mt-10 sm:mt-16 mb-16">
          {ChatPageStaticData.categories.map((category, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl shadow-sm hover:shadow-md transition p-4 sm:p-5 min-h-[360px] flex flex-col justify-between"
            >
              <div className="flex items-center mb-4 font-semibold text-base sm:text-lg">
                <span className="mr-2 text-lg sm:text-xl text-pink-500">
                  {React.createElement(
                    iconMapping[category.icon as keyof typeof iconMapping],
                    {
                      className: "w-5 h-5",
                    }
                  )}
                </span>
                {category.title}
              </div>
              <div className="space-y-3">
                {category.items.map((item, i) => (
                  <Link
                    to={`/products?search=${encodeURIComponent(
                      item.title.trim().toLowerCase()
                    )}`}
                    key={i}
                    className="flex items-center space-x-3"
                  >
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-12 h-12 sm:w-14 sm:h-14 rounded-xl object-cover"
                    />
                    <p className="text-sm text-gray-800 line-clamp-2">
                      {item.title}
                    </p>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={modalOpen}
        onClose={closeModal}
        step={step}
        setStep={setStep}
      />
    </>
  );
};

export default ChatPage;
