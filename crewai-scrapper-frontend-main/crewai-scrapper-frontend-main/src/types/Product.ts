export interface Product {
  id: string;
  title: string;
  price: string;
  imageUrl: string;
  minimumOrderQuantity: string;
  platform: string;
  registeredSupplier: boolean;
  supplierName: string;
  registerTime: string;
  rating: number;
  customerInterestNumber: number;
  genderCategory: string;
  createdAt: string;
  updatedAt: string;
  vendor: {
    name: string;
    isVerified: boolean;
    numOfProducts: number;
    websiteUrl: string;
  };
}

export interface ProductState {
  products: Product[];
  favorites: string[];
  selectedProducts: string[];
  loading: boolean;
  error: string | null;
}

export interface ScrapingApiResponse {
  products: Array<{
    id?: string;
    title?: string;
    price?: string;
    image?: string;
    minimumOrderQuantity?: string;
    platform?: string;
    registeredSupplier?: boolean;
    supplierName?: string;
    registerTime?: string;
    rating?: number;
    customerInterestNumber?: number;
    gender?: string;
    createdAt?: string;
    updatedAt?: string;
  }>;
  totalCount?: number;
  currentPage?: number;
  hasMore?: boolean;
}
