{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@reduxjs/toolkit": "^2.8.2", "async-mutex": "^0.5.0", "axios": "^1.9.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^6.22.2", "react-toastify": "^11.0.5", "swiper": "^11.2.8"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/redux": "^3.6.31", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}