# React Vite App

This is a modern React application powered by [Vite](https://vitejs.dev/), optimized for fast development and efficient production builds.

---

## Features

- Fast build and hot module replacement (HMR) using Vite
- Modular React components
- Environment-based configuration
- Clean and scalable project structure

---

## Project Structure

```
src/
├── components/    # Reusable UI components
├── pages/         # Route-level pages
├── hooks/         # Custom hooks
├── utils/         # Utility functions
├── App.jsx
├── main.jsx
```

---

## Getting Started

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/your-repo-name.git
cd your-repo-name
```

### 2. Install Dependencies

```bash
npm install
# or
yarn
```

### 3. Configure Environment Variables

Create a `.env` file in the root directory:

```env
VITE_BACKEND_BASE_URL='https://your-backend-url.com/api'
```

This variable should point to your backend server base URL.

### 4. Start the Development Server

```bash
npm run dev
# or
yarn dev
```

Visit `http://localhost:5173` to view the app in your browser.

---

## Available Scripts

- `dev` – Start the development server
- `build` – Build the application for production
- `preview` – Preview the production build locally

---

## Production Build

To build the app for production:

```bash
npm run build
# or
yarn build
```

The output will be available in the `dist/` directory.

---
