import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Ava<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Input,
} from "@material-tailwind/react";
import {
  ChevronDoubleLeftIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronDoubleRightIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { SingleProductContainer } from "./SingleProductDetailsContainer";

export const AllProductsContainer = ({
  data,
  pagination,
  searchTerm,
  setSearchTerm,
  currentPage,
  setCurrentPage,
  isLoading,
  error,
}) => {
  const [selectedProduct, setSelectedProduct] = useState(null);

  const products = data || [];

  const totalPages = pagination?.totalPages || 1;

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Function to truncate text with ellipsis
  const truncateText = (text, maxLength = 60) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  // Function to get platform color
  const getPlatformColor = (platform) => {
    const platformColors = {
      Amazon: "blue",
      AliExpress: "orange",
      Banggood: "green",
      Walmart: "blue-gray",
      eBay: "purple",
      Shopee: "red",
      Lazada: "pink",
      Flipkart: "yellow",
      Target: "red",
      "Best Buy": "blue",
      Newegg: "green",
      Etsy: "orange",
      Wish: "pink",
      Alibaba: "orange",
      "JD.com": "red",
      Tmall: "red",
      Taobao: "orange",
    };

    return platformColors[platform] || "gray";
  };

  const handleProductClick = (product) => {
    setSelectedProduct(product);
  };

  if (isLoading) {
    return (
      <CardBody className="px-0 pt-0 pb-2">
        <Typography className="text-center py-8">
          Loading products...
        </Typography>
      </CardBody>
    );
  }

  if (error) {
    return (
      <CardBody className="px-0 pt-0 pb-2">
        <Typography className="text-center py-8 text-red-500">
          Error loading products: {error.message}
        </Typography>
      </CardBody>
    );
  }

  // Conditional rendering: Show single product view or products list
  return selectedProduct ? (
    <SingleProductContainer
      selectedProduct={selectedProduct}
      setSelectedProduct={setSelectedProduct}
    />
  ) : (
    <Card className="p-8 bg-white min-h-[80vh]">
      <div className="text-left mb-4">
        <Typography variant="h5" color="blue-gray" className="mb-2">
          All Products
        </Typography>
        <Typography variant="paragraph" color="gray" className="text-md">
          Browse and manage all products in your inventory. Use the search bar
          to find specific products or adjust the pagination to view more items.
        </Typography>
      </div>

      <div className="mb-4">
        <Input
          type="text"
          label="Search by product name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full"
          icon={<MagnifyingGlassIcon />}
        />
      </div>

      <CardBody className="px-0 pt-0 pb-2 min-h-[75vh]">
        <table className="w-full table-auto">
          <thead>
            <tr>
              {["Product", "Price", "Vendor", "Platform"].map((el) => (
                <th
                  key={el}
                  className="border-b border-blue-gray-50 py-3 px-5 text-left"
                >
                  <Typography
                    variant="small"
                    className="text-[11px] font-bold uppercase text-blue-gray-400"
                  >
                    {el}
                  </Typography>
                </th>
              ))}
            </tr>
          </thead>

          <tbody>
            {products?.length > 0 &&
              products.map((product, key) => {
                const className = `py-3 px-5 ${
                  key === products.length - 1
                    ? ""
                    : "border-b border-blue-gray-50"
                }`;

                return (
                  <tr
                    key={product.id}
                    className="cursor-pointer hover:bg-blue-gray-50 transition-colors"
                    onClick={() => handleProductClick(product)}
                  >
                    <td className={className}>
                      <div className="flex items-center gap-4">
                        <Avatar
                          src={
                            product.imageUrl ||
                            "/app-img/default_product_img.png"
                          }
                          alt={product.title}
                          size="sm"
                          variant="rounded"
                        />
                        <div className="max-w-xs">
                          <Typography
                            variant="small"
                            color="blue-gray"
                            className="font-semibold"
                            title={product.title}
                          >
                            {truncateText(product.title, 60)}
                          </Typography>
                        </div>
                      </div>
                    </td>
                    <td className={className}>
                      <Typography className="text-xs font-semibold text-blue-gray-600">
                        {product.price}
                      </Typography>
                    </td>
                    <td className={className}>
                      <Typography className="text-xs font-semibold text-blue-gray-600">
                        {product.vendor.name}
                      </Typography>
                    </td>
                    <td className={className}>
                      <Chip
                        variant="gradient"
                        color={getPlatformColor(product.platform)}
                        value={product.platform}
                        className="py-0.5 px-2 text-[11px] font-medium w-fit"
                      />
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </table>

        {products?.length === 0 && (
          <div className="flex flex-col items-center justify-center py-8">
            <Typography variant="h6" color="blue-gray" className="mb-2">
              No Products Found
            </Typography>
            <Typography variant="small" color="blue-gray" className="mb-4">
              Try adjusting your search or check back later.
            </Typography>
            <Button
              variant="outlined"
              size="sm"
              onClick={() => {
                setSearchTerm("");
                setCurrentPage(1);
              }}
              className="flex items-center gap-1"
            >
              Clear Search
            </Button>
          </div>
        )}
      </CardBody>

      {/* Pagination */}
      {products.length > 0 && (
        <div className="mx-4 mt-4">
          <div className="flex items-center justify-between">
            <Typography variant="small" color="blue-gray">
              Page {currentPage} of {totalPages}
            </Typography>

            <div className="flex gap-2">
              {/* First Page */}
              <Button
                variant="text"
                size="sm"
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1}
                className="flex items-center gap-1"
              >
                <ChevronDoubleLeftIcon className="h-4 w-4" />
                First
              </Button>

              {/* Previous Page */}
              <Button
                variant="text"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="flex items-center gap-1"
              >
                <ChevronLeftIcon className="h-4 w-4" />
                Previous
              </Button>

              {/* Next Page */}
              <Button
                variant="text"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1"
              >
                Next
                <ChevronRightIcon className="h-4 w-4" />
              </Button>

              {/* Last Page */}
              <Button
                variant="text"
                size="sm"
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1"
              >
                Last
                <ChevronDoubleRightIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};
