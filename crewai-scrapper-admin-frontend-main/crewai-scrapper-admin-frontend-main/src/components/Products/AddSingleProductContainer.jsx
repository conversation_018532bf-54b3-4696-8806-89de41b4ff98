import React, { useState } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import {
  Input,
  Checkbox,
  Select,
  Option,
  Button,
  Card,
  CardBody,
  Typography,
  Alert,
  Tooltip,
  Dialog,
  DialogBody,
  DialogHeader,
  IconButton,
  Spinner,
  Radio,
} from "@material-tailwind/react";
import {
  PhotoIcon,
  TagIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  UserIcon,
  StarIcon,
  UsersIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  XMarkIcon,
  BuildingStorefrontIcon,
  CheckIcon,
  LinkIcon,
} from "@heroicons/react/24/solid";
//import { LinkIcon } from "@heroicons/react/24/solid";

import { useAddSingleProductMutation } from "@/api/services/Products/ProductsService";
import { useGetVendorListQuery } from "@/api/services/Vendors/VendorsService";

// Enhanced validation schema
const createValidationSchema = (vendorChoice) => {
  const baseSchema = {
    image: Yup.string()
      .url("Please enter a valid URL")
      .required("Product image is required"),
    productUrl: Yup.string()
      .trim()
      .url("Please enter a valid product URL")
      .required("Product URL is required")
      .test(
        "not-empty",
        "Product URL cannot be empty",
        (value) => !!value && value.trim() !== ""
      ),

    title: Yup.string()
      .min(3, "Title must be at least 3 characters")
      .max(100, "Title cannot exceed 100 characters")
      .required("Product title is required"),
    price: Yup.string().required("Price is required"),
    minimumOrderQuantity: Yup.number()
      .integer("Must be a whole number")
      .min(1, "Minimum quantity must be at least 1")
      .required("Minimum order quantity is required"),
    platform: Yup.string().required("Platform is required"),
    rating: Yup.number()
      .min(0, "Rating must be between 0 and 5")
      .max(5, "Rating must be between 0 and 5")
      .required("Rating is required"),
    customerInterestNumber: Yup.number()
      .integer("Must be a whole number")
      .min(0, "Cannot be negative")
      .required("Customer interest number is required"),
    gender: Yup.string().required("Target audience is required"),
    category: Yup.string().required("Category is required"),
    registerTime: Yup.date()
      .max(new Date(), "Register time cannot be in the future")
      .required("Register time is required"),
    vendorChoice: Yup.string().required("Please select vendor option"),
  };

  if (vendorChoice === "existing") {
    baseSchema.selectedVendorId = Yup.string().required(
      "Please select a vendor"
    );
  } else if (vendorChoice === "new") {
    baseSchema.vendorName = Yup.string()
      .min(2, "Vendor name must be at least 2 characters")
      .required("Vendor name is required");

    baseSchema.vendorRegisterOn = Yup.date()
      .max(new Date(), "Register date cannot be in the future")
      .required("Vendor registration date is required");

    baseSchema.websiteUrl = Yup.string()
      .url("Please enter a valid website URL")
      .required("Website URL is required");

    baseSchema.numOfProducts = Yup.number()
      .integer("Must be a whole number")
      .min(0, "Cannot be negative")
      .required("Number of products is required");

    baseSchema.country = Yup.string().required("Country is required");
    baseSchema.city = Yup.string().nullable();
    baseSchema.region = Yup.string().nullable();
    baseSchema.isVerified = Yup.boolean();
  }

  return Yup.object(baseSchema);
};

export const AddSingleProductContainer = ({ onProductAdded }) => {
  const [imagePreview, setImagePreview] = useState("");
  const [submitStatus, setSubmitStatus] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [vendorChoice, setVendorChoice] = useState(""); // 'existing' or 'new'

  const [addSingleProduct] = useAddSingleProductMutation();

  // Fetch vendors for dropdown
  const {
    data: vendorsData,
    isLoading: vendorsLoading,
    error: vendorsError,
  } = useGetVendorListQuery();

  const vendors = React.useMemo(() => {
    if (!vendorsData) return [];

    if (
      vendorsData.data &&
      vendorsData.data.vendors &&
      Array.isArray(vendorsData.data.vendors)
    ) {
      return vendorsData.data.vendors;
    }

    if (Array.isArray(vendorsData)) {
      return vendorsData;
    } else if (vendorsData.vendors && Array.isArray(vendorsData.vendors)) {
      return vendorsData.vendors;
    } else if (vendorsData.data && Array.isArray(vendorsData.data)) {
      return vendorsData.data;
    }

    return [];
  }, [vendorsData]);

  const initialValues = {
    image: "",
    title: "",
    price: "",
    productUrl: "",
    minimumOrderQuantity: "",
    platform: "",
    otherPlatform: "",
    rating: "",
    customerInterestNumber: 0,
    gender: "all",
    category: "",
    registerTime: "",
    vendorChoice: "",
    // For existing vendor
    selectedVendorId: "",
    // For new vendor
    vendorName: "",
    vendorRegisterOn: "",
    websiteUrl: "",
    numOfProducts: 0,
    country: "",
    city: "",
    region: "",
    isVerified: false,
  };

  const [imageAccessibility, setImageAccessibility] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const checkImageAccessibility = (url) => {
    if (!url) {
      setImageAccessibility(null);
      return;
    }

    setImageAccessibility("checking");

    const img = new Image();
    img.src = url;

    img.onload = () => {
      setImageAccessibility("accessible");
    };

    img.onerror = () => {
      setImageAccessibility("inaccessible");
    };
  };

  const handleImageChange = (url, setFieldValue) => {
    setFieldValue("image", url);
    setImagePreview(url);
  };

  const handleSubmit = async (values, { resetForm }) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      let payload;

      // Prepare the product data
      const productData = {
        image: values.image,
        title: values.title,
        productUrl: values.productUrl,
        price: values.price.toString(), // Convert to string for backend
        minimumOrderQuantity: values.minimumOrderQuantity.toString(),
        platform:
          values.platform === "other" ? values.otherPlatform : values.platform,
        registerTime: values.registerTime,
        rating: parseFloat(values.rating),
        customerInterestNumber: parseInt(values.customerInterestNumber),
        gender: values.gender,
        category: values.category,
      };

      if (values.vendorChoice === "existing") {
        // Use existing vendor
        payload = {
          productData,
          vendorId: values.selectedVendorId,
        };
      } else {
        // Create new vendor
        const vendorData = {
          name: values.vendorName,
          registerOn: values.vendorRegisterOn,
          websiteUrl: values.websiteUrl,
          numOfProducts: parseInt(values.numOfProducts),
          country: values.country,
          city: values.city || undefined,
          region: values.region || undefined,
          isVerified: values.isVerified,
        };

        payload = {
          productData,
          vendorData,
        };
      }

      // Call the API
      const response = await addSingleProduct(payload).unwrap();

      setSubmitStatus({
        type: "success",
        message: "Product added successfully!",
      });

      onProductAdded(response.data);

      resetForm();
      setImagePreview("");
      setImageAccessibility(null);
      setVendorChoice("");

      // Clear submitStatus after 3 seconds
      setTimeout(() => {
        setSubmitStatus(null);
      }, 3000);
    } catch (error) {
      console.error("Failed to add product:", error);
      setSubmitStatus({
        type: "error",
        message:
          error?.data?.message || "Failed to add product. Please try again.",
      });

      // Clear submitStatus after 3 seconds
      setTimeout(() => {
        setSubmitStatus(null);
      }, 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {" "}
      {/* Status Alert */}
      {submitStatus && (
        <Alert
          color={submitStatus.type === "success" ? "green" : "red"}
          icon={
            submitStatus.type === "success" ? (
              <CheckCircleIcon className="h-6 w-6" />
            ) : (
              <ExclamationTriangleIcon className="h-6 w-6" />
            )
          }
          className="mb-3"
        >
          {submitStatus.message}
        </Alert>
      )}
      <Card className="w-full">
        <CardBody className="p-8">
          {/* Header */}
          <div className="text-left mb-4">
            <Typography variant="h5" color="blue-gray" className="mb-2">
              Add New Product
            </Typography>
            <Typography variant="paragraph" color="gray" className="text-md">
              Fill in the details below to add a new product to your inventory
            </Typography>
          </div>

          <Formik
            initialValues={initialValues}
            validationSchema={createValidationSchema(vendorChoice)}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ values, handleChange, handleBlur, setFieldValue }) => (
              <Form className="space-y-6">
                {/* Image Section with Preview */}
                <Card className="bg-gray-50 border">
                  <CardBody className="p-6 flex justify-start items-end">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 w-full">
                      {/* input container */}
                      <div className="flex flex-col justify-between items-start w-full">
                        <Typography
                          variant="h6"
                          color="blue-gray"
                          className="mb-4 w-full"
                        >
                          Product Image
                        </Typography>

                        <Field name="image" className="w-full">
                          {({ field, meta }) => (
                            <div className="w-full">
                              <Input
                                {...field}
                                label="Image URL"
                                icon={<PhotoIcon className="h-5 w-5" />}
                                error={meta.touched && meta.error}
                                onChange={(e) => {
                                  field.onChange(e);
                                  handleImageChange(
                                    e.target.value,
                                    setFieldValue
                                  );
                                  checkImageAccessibility(e.target.value);
                                }}
                                className="bg-white w-full"
                              />
                              {!imageAccessibility && (
                                <ErrorMessage
                                  name="image"
                                  component="div"
                                  className="text-red-500 text-sm mt-1"
                                />
                              )}
                              {imageAccessibility === "checking" && (
                                <Typography
                                  variant="small"
                                  className="text-blue-500 mt-1"
                                >
                                  Checking image accessibility...
                                </Typography>
                              )}
                              {imageAccessibility === "accessible" && (
                                <Typography
                                  variant="small"
                                  className="text-green-500 mt-1"
                                >
                                  Image is accessible and previewable.
                                </Typography>
                              )}
                              {imageAccessibility === "inaccessible" && (
                                <Typography
                                  variant="small"
                                  className="text-red-500 mt-1"
                                >
                                  Image URL is not accessible or invalid.
                                </Typography>
                              )}
                            </div>
                          )}
                        </Field>
                      </div>

                      {/* preview container */}
                      <div className="flex justify-center">
                        <div className="relative rounded-lg border overflow-hidden">
                          {imagePreview &&
                          imageAccessibility === "accessible" ? (
                            <img
                              src={imagePreview}
                              alt="Product preview"
                              className="w-[10rem] h-[10rem] object-cover border-gray-200"
                              onError={() => setImagePreview("")}
                            />
                          ) : (
                            <div className="w-[10rem] h-[10rem] bg-gray-400 flex justify-center items-center">
                              <p className="text-white">Image Preview</p>
                            </div>
                          )}
                          <Tooltip
                            content={
                              imagePreview
                                ? "Image preview"
                                : "No Preview Available"
                            }
                          >
                            <EyeIcon
                              className="h-5 w-5 absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded p-1 cursor-pointer"
                              onClick={() => {
                                if (imagePreview) setIsModalOpen(true);
                              }}
                            />
                          </Tooltip>
                        </div>
                      </div>
                    </div>
                  </CardBody>

                  {/* Modal for Full-Size Image */}
                  <Dialog
                    open={isModalOpen}
                    handler={() => setIsModalOpen(false)}
                  >
                    <DialogHeader className="flex justify-end items-center w-full">
                      <IconButton
                        variant="text"
                        onClick={() => setIsModalOpen(false)}
                        color="blue-gray"
                        className="rounded-full"
                      >
                        <XMarkIcon className="h-5 w-5 text-blue-gray-500" />
                      </IconButton>
                    </DialogHeader>

                    <DialogBody className="flex justify-center items-center w-full pb-5">
                      <img
                        src={values.image}
                        alt="Product preview"
                        className="w-[20rem] h-auto rounded-lg"
                      />
                    </DialogBody>
                  </Dialog>
                </Card>

                {/* Basic Information */}
                <Card className="bg-gray-50 border">
                  <CardBody className="p-6">
                    <Typography variant="h6" color="blue-gray" className="mb-4">
                      Basic Information
                    </Typography>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Field name="title">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              label="Product Title"
                              icon={<TagIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="title"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div>
                        <Field name="productUrl">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              label="Product URL"
                              icon={<LinkIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="productUrl"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div>
                        <Field name="price">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              type="string"
                              label="Price"
                              icon={<CurrencyDollarIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="price"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div>
                        <Field name="minimumOrderQuantity">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              type="number"
                              label="Minimum Order Quantity"
                              icon={<ShoppingCartIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="minimumOrderQuantity"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div>
                        <Field name="platform">
                          {({ field, meta }) => (
                            <Select
                              {...field}
                              label="Platform"
                              value={values.platform}
                              onChange={(value) =>
                                setFieldValue("platform", value)
                              }
                              className="bg-white"
                            >
                              <Option value="amazon">Amazon</Option>
                              <Option value="ebay">eBay</Option>
                              <Option value="shopify">Shopify</Option>
                              <Option value="etsy">Etsy</Option>
                              <Option value="other">Other</Option>
                            </Select>
                          )}
                        </Field>
                        <ErrorMessage
                          name="platform"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      {/* Manual Input for "Other" Platform */}
                      {values.platform === "other" && (
                        <div>
                          <Field name="otherPlatform">
                            {({ field, meta }) => (
                              <Input
                                {...field}
                                label="Other Platform Name"
                                icon={
                                  <BuildingStorefrontIcon className="h-5 w-5" />
                                }
                                error={meta.touched && meta.error}
                                className="bg-white"
                              />
                            )}
                          </Field>
                          <ErrorMessage
                            name="otherPlatform"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                      )}

                      <div>
                        <Field name="category">
                          {({ field, meta }) => (
                            <Select
                              {...field}
                              label="Category"
                              value={values.category}
                              onChange={(value) =>
                                setFieldValue("category", value)
                              }
                              className="bg-white"
                            >
                              <Option value="electronics">Electronics</Option>
                              <Option value="clothing">Clothing</Option>
                              <Option value="home">Home & Garden</Option>
                              <Option value="sports">Sports & Outdoors</Option>
                              <Option value="books">Books</Option>
                              <Option value="toys">Toys & Games</Option>
                              <Option value="beauty">
                                Beauty & Personal Care
                              </Option>
                              <Option value="automotive">Automotive</Option>
                              <Option value="other">Other</Option>
                            </Select>
                          )}
                        </Field>
                        <ErrorMessage
                          name="category"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div>
                        <Field name="registerTime">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              type="date"
                              label="Product Registration Date"
                              className="bg-white"
                              max={new Date().toISOString().split("T")[0]}
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="registerTime"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Vendor Selection */}
                <Card className="bg-gray-50 border">
                  <CardBody className="p-6">
                    <Typography variant="h6" color="blue-gray" className="mb-4">
                      Vendor Selection
                    </Typography>

                    <div className="mb-6">
                      <Typography
                        variant="small"
                        color="blue-gray"
                        className="mb-3 font-medium"
                      >
                        Choose vendor option:
                      </Typography>
                      <div className="flex flex-col gap-2">
                        <Field name="vendorChoice">
                          {({ field }) => (
                            <>
                              <div className="flex items-center">
                                <Radio
                                  name="vendorChoice"
                                  value="existing"
                                  checked={values.vendorChoice === "existing"}
                                  onChange={(e) => {
                                    setFieldValue("vendorChoice", "existing");
                                    setVendorChoice("existing");
                                    // Clear new vendor fields
                                    setFieldValue("vendorName", "");
                                    setFieldValue("vendorRegisterOn", "");
                                    setFieldValue("isVerified", false);
                                    setFieldValue("websiteUrl", "");
                                    setFieldValue("numOfProducts", 0);
                                  }}
                                  className="text-blue-500"
                                />
                                <Typography
                                  variant="small"
                                  className="ml-2 font-medium"
                                >
                                  Use Existing Vendor
                                </Typography>
                              </div>
                              <div className="flex items-center">
                                <Radio
                                  name="vendorChoice"
                                  value="new"
                                  checked={values.vendorChoice === "new"}
                                  onChange={(e) => {
                                    setFieldValue("vendorChoice", "new");
                                    setVendorChoice("new");
                                    // Clear existing vendor field
                                    setFieldValue("selectedVendorId", "");
                                  }}
                                  className="text-blue-500"
                                />
                                <Typography
                                  variant="small"
                                  className="ml-2 font-medium"
                                >
                                  Create New Vendor
                                </Typography>
                              </div>
                            </>
                          )}
                        </Field>
                      </div>
                      <ErrorMessage
                        name="vendorChoice"
                        component="div"
                        className="text-red-500 text-sm mt-1"
                      />
                    </div>

                    {/* Existing Vendor Selection */}
                    {values.vendorChoice === "existing" && (
                      <div className="mb-4">
                        <Field name="selectedVendorId">
                          {({ field, meta }) => (
                            <div>
                              {vendorsLoading ? (
                                <div className="flex items-center justify-center p-4">
                                  <Spinner className="h-6 w-6" />
                                  <Typography variant="small" className="ml-2">
                                    Loading vendors...
                                  </Typography>
                                </div>
                              ) : vendorsError ? (
                                <Alert color="red" className="mb-4">
                                  Failed to load vendors. Please try again.
                                </Alert>
                              ) : (
                                <Select
                                  {...field}
                                  label="Select Vendor"
                                  value={values.selectedVendorId}
                                  onChange={(value) =>
                                    setFieldValue("selectedVendorId", value)
                                  }
                                  className="bg-white"
                                >
                                  {vendors.map((vendor) => (
                                    <Option key={vendor.id} value={vendor.id}>
                                      {vendor.name} {vendor.isVerified && "✓"}
                                    </Option>
                                  ))}
                                </Select>
                              )}
                              <ErrorMessage
                                name="selectedVendorId"
                                component="div"
                                className="text-red-500 text-sm mt-1"
                              />
                            </div>
                          )}
                        </Field>
                      </div>
                    )}

                    {/* New Vendor Form */}
                    {values.vendorChoice === "new" && (
                      <div className="space-y-4">
                        <Typography
                          variant="h6"
                          color="blue-gray"
                          className="mb-4"
                        >
                          New Vendor Information
                        </Typography>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Field name="vendorName">
                              {({ field, meta }) => (
                                <Input
                                  {...field}
                                  label="Vendor Name"
                                  icon={<UserIcon className="h-5 w-5" />}
                                  error={meta.touched && meta.error}
                                  className="bg-white"
                                />
                              )}
                            </Field>
                            <ErrorMessage
                              name="vendorName"
                              component="div"
                              className="text-red-500 text-sm mt-1"
                            />
                          </div>

                          <div>
                            <Field name="vendorRegisterOn">
                              {({ field, meta }) => (
                                <Input
                                  {...field}
                                  type="date"
                                  label="Vendor Registration Date"
                                  className="bg-white"
                                  max={new Date().toISOString().split("T")[0]}
                                />
                              )}
                            </Field>
                            <ErrorMessage
                              name="vendorRegisterOn"
                              component="div"
                              className="text-red-500 text-sm mt-1"
                            />
                          </div>

                          <div>
                            <Field name="websiteUrl">
                              {({ field, meta }) => (
                                <Input
                                  {...field}
                                  label="Website URL"
                                  icon={
                                    <BuildingStorefrontIcon className="h-5 w-5" />
                                  }
                                  error={meta.touched && meta.error}
                                  className="bg-white"
                                />
                              )}
                            </Field>
                            <ErrorMessage
                              name="websiteUrl"
                              component="div"
                              className="text-red-500 text-sm mt-1"
                            />
                          </div>

                          <div>
                            <Field name="numOfProducts">
                              {({ field, meta }) => (
                                <Input
                                  {...field}
                                  type="number"
                                  label="Number of Products"
                                  icon={
                                    <ShoppingCartIcon className="h-5 w-5" />
                                  }
                                  error={meta.touched && meta.error}
                                  className="bg-white"
                                />
                              )}
                            </Field>
                            <ErrorMessage
                              name="numOfProducts"
                              component="div"
                              className="text-red-500 text-sm mt-1"
                            />
                          </div>

                          {/* Country */}
                          <div>
                            <Field name="country">
                              {({ field, meta }) => (
                                <Input
                                  {...field}
                                  label="Country"
                                  error={meta.touched && meta.error}
                                  className="bg-white"
                                />
                              )}
                            </Field>
                            <ErrorMessage
                              name="country"
                              component="div"
                              className="text-red-500 text-sm mt-1"
                            />
                          </div>

                          {/* City (optional) */}
                          <div>
                            <Field name="city">
                              {({ field }) => (
                                <Input
                                  {...field}
                                  label="City (Optional)"
                                  className="bg-white"
                                />
                              )}
                            </Field>
                          </div>

                          {/* Region (optional) */}
                          <div>
                            <Field name="region">
                              {({ field }) => (
                                <Input
                                  {...field}
                                  label="Region (Optional)"
                                  className="bg-white"
                                />
                              )}
                            </Field>
                          </div>
                        </div>

                        <div className="flex justify-start items-center w-full">
                          <Field name="isVerified">
                            {({ field }) => (
                              <Checkbox
                                {...field}
                                label="Is Verified Vendor"
                                checked={values.isVerified}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                className="text-blue-500"
                              />
                            )}
                          </Field>
                        </div>
                      </div>
                    )}
                  </CardBody>
                </Card>

                {/* Performance Metrics */}
                <Card className="bg-gray-50 border">
                  <CardBody className="p-6">
                    <Typography variant="h6" color="blue-gray" className="mb-4">
                      Performance Metrics
                    </Typography>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Field name="rating">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              type="number"
                              step="0.1"
                              min="0"
                              max="5"
                              label="Rating (0-5)"
                              icon={<StarIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="rating"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div>
                        <Field name="customerInterestNumber">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              type="number"
                              label="Customer Interest Count"
                              icon={<UsersIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="customerInterestNumber"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      <div>
                        <Field name="gender">
                          {({ field, meta }) => (
                            <Select
                              {...field}
                              label="Target Audience"
                              value={values.gender}
                              onChange={(value) =>
                                setFieldValue("gender", value)
                              }
                              className="bg-white"
                            >
                              <Option value="all">All</Option>
                              <Option value="men">Men</Option>
                              <Option value="women">Women</Option>
                              <Option value="kids">Kids</Option>
                              <Option value="unisex">Unisex</Option>
                            </Select>
                          )}
                        </Field>
                        <ErrorMessage
                          name="gender"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Submit Button */}
                <div className="flex justify-end pt-6">
                  <Button
                    type="submit"
                    color="green"
                    size="lg"
                    className="flex justify-center items-center w-full md:w-auto px-8 py-3"
                    loading={isSubmitting ? "true" : undefined}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <Spinner className="text-white h-5 w-5 mr-2 animate-spin" />
                    ) : (
                      <CheckIcon className="text-white h-5 w-5 mr-2" />
                    )}{" "}
                    {isSubmitting ? "Adding Product..." : "Add Product"}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </CardBody>
      </Card>
    </>
  );
};
