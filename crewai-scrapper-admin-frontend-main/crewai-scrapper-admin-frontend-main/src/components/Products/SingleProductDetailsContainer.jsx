import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Card,
  CardBody,
  Typography,
  <PERSON>ton,
  Chip,
  CardHeader,
} from "@material-tailwind/react";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";

export const SingleProductContainer = ({
  selectedProduct,
  setSelectedProduct,
}) => {
  // Function to get platform color
  const getPlatformColor = (platform) => {
    const platformColors = {
      Amazon: "blue",
      AliExpress: "orange",
      Banggood: "green",
      Walmart: "blue-gray",
      eBay: "purple",
      Shopee: "red",
      <PERSON><PERSON><PERSON>: "pink",
      Flipkart: "yellow",
      Target: "red",
      "Best Buy": "blue",
      Newegg: "green",
      Etsy: "orange",
      Wish: "pink",
      Alibaba: "orange",
      "JD.com": "red",
      Tmall: "red",
      Taobao: "orange",
    };

    return platformColors[platform] || "gray";
  };

  // Function to format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Function to get only year from date
  const getYearOnly = (dateString) => {
    const date = new Date(dateString);
    return date.getFullYear().toString();
  };

  const handleBack = () => {
    setSelectedProduct(null);
  };

  if (!selectedProduct) {
    return (
      <div className="flex items-center justify-center h-64">
        <Typography variant="h6" color="red">
          Product not found. Please go back to products list.
        </Typography>
        <Button onClick={handleBack} className="ml-4">
          Back to Products
        </Button>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white min-h-[80vh] rounded-md">
      {/* Back Button */}
      <div className="mb-12">
        <Button
          variant="text"
          className="flex items-center gap-2"
          onClick={handleBack}
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back to Products
        </Button>
      </div>

      {/* Product Details Card */}
      <Card className="max-w-6xl mx-auto bg-white shadow-none border border-gray-200">
        <CardHeader className="bg-white p-6 border-b border-gray-200 border">
          <div className="flex items-start gap-8">
            <div className="flex-shrink-0">
              <img
                src={
                  selectedProduct.imageUrl || "/app-img/default_product_img.png"
                }
                alt={selectedProduct.title}
                className="h-64 w-64 object-contain rounded-lg border border-gray-200"
              />
            </div>

            <div className="flex-1">
              <Typography variant="h4" color="blue-gray" className="mb-4">
                {selectedProduct.title}
              </Typography>
              <Typography variant="h3" color="green" className="font-bold">
                {selectedProduct.price}
              </Typography>
            </div>
          </div>
        </CardHeader>

        <CardBody className="p-6 bg-white">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <Typography variant="h6" color="blue-gray" className="mb-2">
                  Supplier Information
                </Typography>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <Typography variant="paragraph" className="font-semibold">
                    {selectedProduct.supplierName}
                  </Typography>
                  <Typography variant="small" color="gray" className="mt-1">
                    Registered In: {getYearOnly(selectedProduct.registerTime)}
                  </Typography>
                  <Chip
                    variant="ghost"
                    color={selectedProduct.registeredSupplier ? "green" : "red"}
                    value={
                      selectedProduct.registeredSupplier
                        ? "Verified Supplier"
                        : "Unverified Supplier"
                    }
                    className="mt-2"
                  />
                </div>
              </div>

              <div>
                <Typography variant="h6" color="blue-gray" className="mb-2">
                  Order Details
                </Typography>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <Typography variant="paragraph">
                    <span className="font-semibold">Minimum Order:</span>{" "}
                    {selectedProduct.minimumOrderQuantity}
                  </Typography>
                </div>
              </div>

              <div>
                <Typography variant="h6" color="blue-gray" className="mb-2">
                  Customer Interest
                </Typography>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <Typography variant="paragraph">
                    <span className="font-semibold">Interest Level:</span>{" "}
                    {selectedProduct.customerInterestNumber} customers
                  </Typography>
                  <Typography variant="paragraph" className="mt-2">
                    <span className="font-semibold">Rating:</span> ⭐{" "}
                    {selectedProduct.rating}/5.0
                  </Typography>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <Typography variant="h6" color="blue-gray" className="mb-2">
                  Product Details
                </Typography>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <Typography variant="paragraph">
                    <span className="font-semibold">Target Gender:</span>{" "}
                    {selectedProduct.gender}
                  </Typography>
                </div>
              </div>

              <div>
                <Typography variant="h6" color="blue-gray" className="mb-2">
                  Platform Information
                </Typography>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <Typography
                    variant="paragraph"
                    className="flex items-center gap-2"
                  >
                    <span className="font-semibold">Platform:</span>
                    <span
                      className="px-3 py-1 rounded-full text-white font-medium"
                      style={{
                        backgroundColor:
                          getPlatformColor(selectedProduct.platform) === "blue"
                            ? "#3B82F6"
                            : getPlatformColor(selectedProduct.platform) ===
                              "orange"
                            ? "#F97316"
                            : getPlatformColor(selectedProduct.platform) ===
                              "green"
                            ? "#10B981"
                            : getPlatformColor(selectedProduct.platform) ===
                              "blue-gray"
                            ? "#64748B"
                            : getPlatformColor(selectedProduct.platform) ===
                              "purple"
                            ? "#8B5CF6"
                            : getPlatformColor(selectedProduct.platform) ===
                              "red"
                            ? "#EF4444"
                            : getPlatformColor(selectedProduct.platform) ===
                              "pink"
                            ? "#EC4899"
                            : getPlatformColor(selectedProduct.platform) ===
                              "yellow"
                            ? "#EAB308"
                            : getPlatformColor(selectedProduct.platform) ===
                              "gray"
                            ? "#6B7280"
                            : "#6B7280",
                      }}
                    >
                      {selectedProduct.platform}
                    </span>
                  </Typography>
                </div>
              </div>

              <div>
                <Typography variant="h6" color="blue-gray" className="mb-2">
                  Date Information
                </Typography>
                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <Typography variant="paragraph">
                    <span className="font-semibold">Date Created:</span>{" "}
                    {formatDate(selectedProduct.createdAt)}
                  </Typography>
                  <Typography variant="paragraph">
                    <span className="font-semibold">Last Updated:</span>{" "}
                    {formatDate(selectedProduct.updatedAt)}
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};
