import React, { useState } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import {
  Input,
  Checkbox,
  Button,
  Card,
  CardBody,
  Typography,
  Alert,
  Spinner,
} from "@material-tailwind/react";
import {
  UserIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CheckIcon,
  GlobeAltIcon,
  CubeIcon,
} from "@heroicons/react/24/solid";
import { useAddSingleVendorMutation } from "@/api/services/Vendors/VendorsService";

// Updated validation schema to match the Joi validation
const validationSchema = Yup.object({
  name: Yup.string()
    .trim()
    .max(255, "Vendor name should have a maximum length of 255 characters")
    .required("Vendor name is required"),
  registerOn: Yup.date().optional(),
  isVerified: Yup.boolean().optional(),
  websiteUrl: Yup.string()
    .url("Website URL must be a valid URL")
    .max(500, "Website URL should have a maximum length of 500 characters")
    .optional(),
  numOfProducts: Yup.number()
    .integer("Number of products must be an integer")
    .min(1, "Number of products must be at least 1")
    .optional(),
  country: Yup.string()
    .max(100, "Country must be at most 100 characters")
    .required("Country is required"),
  city: Yup.string().max(100, "City must be at most 100 characters").optional(),
  region: Yup.string()
    .max(100, "Region must be at most 100 characters")
    .optional(),
});

export const AddSingleVendorContainer = () => {
  const [submitStatus, setSubmitStatus] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const initialValues = {
    name: "",
    registerOn: "",
    isVerified: false,
    websiteUrl: "",
    numOfProducts: 1,
    country: "",
    city: "",
    region: "",
  };

  const [addSingleVendor, { isLoading, error }] = useAddSingleVendorMutation();

  const handleSubmit = async (values, { resetForm }) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // Prepare the payload according to the Joi schema
      const payload = {
        name: values.name.trim(),
        ...(values.registerOn && { registerOn: values.registerOn }),
        isVerified: values.isVerified,
        ...(values.websiteUrl && { websiteUrl: values.websiteUrl }),
        ...(values.numOfProducts && {
          numOfProducts: parseInt(values.numOfProducts),
        }),
        country: values.country,
        ...(values.city && { city: values.city }),
        ...(values.region && { region: values.region }),
      };

      // Make API call here
      const response = await addSingleVendor({ vendorData: payload }).unwrap();

      setSubmitStatus({
        type: "success",
        message: "Vendor added successfully!",
      });

      resetForm();

      // Clear submitStatus after 3 seconds
      setTimeout(() => {
        setSubmitStatus(null);
      }, 3000);
    } catch (error) {
      setSubmitStatus({
        type: "error",
        message:
          error?.data?.message ||
          error?.message ||
          "Failed to add vendor. Please try again.",
      });

      // Clear submitStatus after 3 seconds
      setTimeout(() => {
        setSubmitStatus(null);
      }, 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Status Alert */}
      {submitStatus && (
        <Alert
          color={submitStatus.type === "success" ? "green" : "red"}
          icon={
            submitStatus.type === "success" ? (
              <CheckCircleIcon className="h-6 w-6" />
            ) : (
              <ExclamationTriangleIcon className="h-6 w-6" />
            )
          }
          className="mb-6"
        >
          {submitStatus.message}
        </Alert>
      )}

      <Card className="w-full">
        <CardBody className="p-8">
          {/* Header */}
          <div className="text-left mb-6">
            <Typography variant="h5" color="blue-gray" className="mb-2">
              Add New Vendor
            </Typography>
            <Typography variant="paragraph" color="gray" className="text-md">
              Fill in the details below to add a new vendor to your system
            </Typography>
          </div>

          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ values, handleChange, handleBlur, setFieldValue }) => (
              <Form className="space-y-6">
                {/* Basic Information */}
                <Card className="bg-gray-50 border">
                  <CardBody className="p-6">
                    <Typography variant="h6" color="blue-gray" className="mb-4">
                      Vendor Information
                    </Typography>

                    <div className="space-y-4">
                      {/* Vendor Name */}
                      <div>
                        <Field name="name">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              label="Vendor Name *"
                              icon={<UserIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                              maxLength={255}
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="name"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      {/* Registration Date */}
                      <div>
                        <Field name="registerOn">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              type="date"
                              label="Registration Date (Optional)"
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="registerOn"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      {/* Website URL */}
                      <div>
                        <Field name="websiteUrl">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              label="Website URL (Optional)"
                              icon={<GlobeAltIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                              maxLength={500}
                              placeholder="https://example.com"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="websiteUrl"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      {/* Number of Products */}
                      <div>
                        <Field name="numOfProducts">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              type="number"
                              min="1"
                              label="Number of Products (Optional)"
                              icon={<CubeIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="numOfProducts"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>
                      {/* Country (Required) */}
                      <div>
                        <Field name="country">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              label="Country *"
                              icon={<GlobeAltIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="country"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      {/* City (Optional) */}
                      <div>
                        <Field name="city">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              label="City (Optional)"
                              icon={<GlobeAltIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="city"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      {/* Region (Optional) */}
                      <div>
                        <Field name="region">
                          {({ field, meta }) => (
                            <Input
                              {...field}
                              label="Region (Optional)"
                              icon={<GlobeAltIcon className="h-5 w-5" />}
                              error={meta.touched && meta.error}
                              className="bg-white"
                            />
                          )}
                        </Field>
                        <ErrorMessage
                          name="region"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      {/* Verification Status */}
                      <div className="flex justify-start items-center w-full pt-2">
                        <Field name="isVerified">
                          {({ field }) => (
                            <Checkbox
                              {...field}
                              label="Is Verified Vendor"
                              checked={values.isVerified}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              className="text-blue-500"
                            />
                          )}
                        </Field>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Submit Button */}
                <div className="flex justify-end pt-6">
                  <Button
                    type="submit"
                    color="green"
                    size="lg"
                    className="flex justify-center items-center w-full md:w-auto px-8 py-3"
                    loading={isSubmitting ? "true" : undefined}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <Spinner className="text-white h-5 w-5 mr-2 animate-spin" />
                    ) : (
                      <CheckIcon className="text-white h-5 w-5 mr-2" />
                    )}
                    {isSubmitting ? "Adding Vendor..." : "Add Vendor"}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </CardBody>
      </Card>
    </>
  );
};
