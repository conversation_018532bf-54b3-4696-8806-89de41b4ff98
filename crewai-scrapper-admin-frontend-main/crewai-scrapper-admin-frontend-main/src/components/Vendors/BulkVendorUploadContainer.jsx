import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ody, Typo<PERSON>, Button } from "@material-tailwind/react";
import {
  CloudArrowUpIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { useAddBulkVendorsMutation } from "@/api/services/Vendors/VendorsService";

export const BulkVendorUploadContainer = () => {
  const [csvFile, setCsvFile] = useState(null);
  const [csvData, setCsvData] = useState([]);
  const [dragActive, setDragActive] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isProcessed, setIsProcessed] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Pagination calculations
  const totalPages = Math.ceil(csvData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = csvData.slice(startIndex, endIndex);

  // CSV parsing for preview
  const parseCSV = (file, callback) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target.result;
      const [headerLine, ...lines] = text.split("\n").filter(Boolean);
      const headers = headerLine.split(",");
      const data = lines.map((line) => {
        const values = line.split(",");
        return headers.reduce((obj, header, i) => {
          obj[header.trim()] = values[i]?.trim() || "";
          return obj;
        }, {});
      });
      callback(data);
    };
    reader.readAsText(file);
  };

  const handleFileSelect = (file) => {
    if (file && file.type === "text/csv") {
      setCsvFile(file);
      setIsProcessed(false);
      // Auto-parse for preview
      parseCSV(file, (data) => {
        setCsvData(data);
        setIsProcessed(true);
        setCurrentPage(1);
      });
    } else {
      alert("Please select a valid CSV file");
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragActive(false);
  };

  const [addBulkVendors, { isLoading: isAddingBulkVendors }] =
    useAddBulkVendorsMutation();

  const handleSave = async () => {
    if (!csvFile) return;

    setIsProcessing(true);

    try {
      const formData = new FormData();
      formData.append("file", csvFile);

      addBulkVendors(formData).unwrap();

      // Show success message immediately
      alert(
        "CSV upload initiated! You will be notified by email once all vendors are processed."
      );

      // Reset form
      setCsvFile(null);
      setCsvData([]);
      setIsProcessed(false);
      setCurrentPage(1);
    } catch (error) {
      console.error("Error initiating upload:", error);
      alert("Failed to initiate upload. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Card className="w-full shadow-lg">
      <CardBody className="p-8">
        <div className="mb-8">
          <Typography variant="h4" color="blue-gray" className="mb-2 font-bold">
            Bulk Vendor Upload
          </Typography>
          <Typography variant="paragraph" color="gray" className="text-sm">
            Upload your vendor data in CSV format to add multiple vendors at
            once
          </Typography>
        </div>

        {/* File Upload Section */}
        <div className="space-y-6 mb-8">
          {!csvFile && (
            <div
              className={`relative border-2 border-dashed rounded-xl p-12 text-center transition-all duration-300 ease-in-out ${
                dragActive
                  ? "border-pink-400 bg-pink-50 scale-[1.02]"
                  : "border-blue-gray-200 bg-gray-50 hover:bg-gray-100"
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <div className="flex flex-col items-center space-y-4">
                <div
                  className={`p-4 rounded-full ${
                    dragActive ? "bg-pink-100" : "bg-blue-gray-100"
                  }`}
                >
                  <CloudArrowUpIcon
                    className={`w-12 h-12 ${
                      dragActive ? "text-pink-500" : "text-blue-gray-400"
                    }`}
                  />
                </div>

                <div className="text-center">
                  <Typography
                    variant="h6"
                    color="blue-gray"
                    className="mb-2 font-semibold"
                  >
                    {dragActive
                      ? "Drop your CSV file here"
                      : "Drag & drop your CSV file here"}
                  </Typography>
                  <Typography variant="small" color="gray" className="mb-6">
                    or click to browse from your computer
                  </Typography>
                </div>

                <input
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />

                <Button
                  variant="outlined"
                  color="blue-gray"
                  size="lg"
                  className="pointer-events-none border-2 hover:border-pink-500 hover:text-pink-500 transition-colors"
                >
                  Browse Files
                </Button>
              </div>
            </div>
          )}

          {/* File Info Display */}
          {csvFile && (
            <div className="bg-white border border-blue-gray-200 rounded-lg p-4 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <DocumentTextIcon className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <Typography
                      variant="small"
                      color="blue-gray"
                      className="font-semibold"
                    >
                      {csvFile.name}
                    </Typography>
                    <Typography variant="small" color="gray">
                      {formatFileSize(csvFile.size)} • {csvData.length} vendors
                    </Typography>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="w-5 h-5 text-green-500" />
                  <Typography
                    variant="small"
                    color="green"
                    className="font-medium"
                  >
                    Ready to process
                  </Typography>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Data Preview Section */}
        {isProcessed && csvData.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center">
                <Typography variant="small" color="white" className="font-bold">
                  ✓
                </Typography>
              </div>
              <Typography
                variant="h6"
                color="blue-gray"
                className="font-semibold"
              >
                Data Preview
              </Typography>
            </div>

            {/* Data Table */}
            <div className="bg-white border border-blue-gray-200 rounded-xl overflow-hidden shadow-sm">
              <div className="px-6 py-4 bg-gradient-to-r from-blue-gray-50 to-gray-50 border-b border-blue-gray-200">
                <Typography
                  variant="h6"
                  color="blue-gray"
                  className="font-semibold"
                >
                  Vendor Data Preview ({csvData.length} vendors)
                </Typography>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      {csvData[0] &&
                        Object.keys(csvData[0]).map((header) => (
                          <th
                            key={header}
                            className="px-6 py-4 text-left text-xs font-bold uppercase text-blue-gray-600 tracking-wider"
                          >
                            {header}
                          </th>
                        ))}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-blue-gray-100">
                    {paginatedData.map((row, idx) => (
                      <tr
                        key={idx}
                        className="hover:bg-blue-gray-50 transition-colors duration-200"
                      >
                        {Object.entries(row).map(([key, val], i) => (
                          <td key={i} className="px-6 py-4 text-sm text-black">
                            {/* Handle logo/image columns */}
                            {(key.toLowerCase().includes("logo") ||
                              key.toLowerCase().includes("image")) &&
                            val &&
                            (val.startsWith("http") ||
                              val.startsWith("data:image")) ? (
                              <img
                                src={val}
                                alt="Logo"
                                className="w-12 h-12 object-cover rounded-lg border border-blue-gray-200 shadow-sm"
                                onError={(e) => {
                                  e.target.style.display = "none";
                                }}
                              />
                            ) : (
                              <span
                                className={
                                  val && val.length > 50
                                    ? "truncate max-w-xs block"
                                    : ""
                                }
                              >
                                {val}
                              </span>
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between bg-white border border-blue-gray-200 rounded-xl p-6 shadow-sm">
                <Typography
                  variant="small"
                  color="gray"
                  className="font-medium"
                >
                  Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                  {Math.min(currentPage * itemsPerPage, csvData.length)} of{" "}
                  {csvData.length} vendors
                </Typography>

                <div className="flex items-center space-x-3">
                  <Button
                    variant="outlined"
                    size="md"
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className="px-4 py-2"
                  >
                    Previous
                  </Button>

                  <div className="flex items-center space-x-2">
                    {[...Array(Math.min(5, totalPages))].map((_, idx) => {
                      const pageNum = idx + 1;
                      return (
                        <Button
                          key={pageNum}
                          variant={
                            currentPage === pageNum ? "gradient" : "outlined"
                          }
                          color={currentPage === pageNum ? "pink" : "blue-gray"}
                          size="md"
                          onClick={() => setCurrentPage(pageNum)}
                          className="w-10 h-10 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outlined"
                    size="md"
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                    }
                    disabled={currentPage === totalPages}
                    className="px-4 py-2"
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}

            {/* Processing Notice */}
            <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
              <div className="flex items-center space-x-2">
                <ClockIcon className="w-5 h-5 text-amber-600" />
                <Typography
                  variant="small"
                  color="amber"
                  className="font-semibold"
                >
                  Processing Notice:
                </Typography>
              </div>
              <Typography variant="small" color="gray" className="mt-1">
                Large CSV files may take several minutes to process. You will
                receive an email notification once all vendors are successfully
                imported.
              </Typography>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t border-blue-gray-100">
              <Button
                variant="outlined"
                color="blue-gray"
                size="lg"
                onClick={() => {
                  setCsvFile(null);
                  setCsvData([]);
                  setIsProcessed(false);
                  setCurrentPage(1);
                }}
                className="px-8 py-3 font-semibold"
                disabled={isProcessing}
              >
                Choose Different File
              </Button>

              <Button
                variant="gradient"
                color="pink"
                size="lg"
                onClick={handleSave}
                disabled={isProcessing || !csvFile}
                className="px-8 py-3 font-semibold shadow-lg"
              >
                {isProcessing
                  ? "Processing..."
                  : `Import ${csvData.length} Vendors`}
              </Button>
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};
