import React, { useEffect, useState, useCallback, useMemo } from "react";
import {
  Card,
  CardBody,
  Typography,
  Button,
  Input,
  Chip,
} from "@material-tailwind/react";

const MultiSelectWithAdd = ({
  title,
  description,
  selectedOptions,
  setSelectedOptions,
  availableOptions,
  setAvailableOptions,
  categoryType,
  menCategories,
  womenCategories,
  bothOtherCategories,
  showAlert,
}) => {
  const [inputValue, setInputValue] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Memoized computation of all selected categories across all types
  const allSelectedCategories = useMemo(() => {
    return [
      ...new Set([
        ...menCategories,
        ...womenCategories,
        ...bothOtherCategories,
      ]),
    ];
  }, [menCategories, womenCategories, bothOtherCategories]);

  // Clear error when input changes
  const handleInputChange = useCallback(
    (e) => {
      setInputValue(e.target.value);
      if (errorMessage) setErrorMessage("");
    },
    [errorMessage]
  );

  // Enhanced duplicate checking across all categories
  const checkForDuplicates = useCallback(
    (newOption) => {
      const trimmedOption = newOption.trim();
      if (!trimmedOption) return "Category name cannot be empty.";

      // Check if already exists in current category
      const existsInCurrent = selectedOptions.some(
        (item) => item.toLowerCase() === trimmedOption.toLowerCase()
      );
      if (existsInCurrent) {
        return `"${trimmedOption}" already exists in this category.`;
      }

      // Check if exists in other categories
      const existsInOther = allSelectedCategories.some(
        (item) =>
          item.toLowerCase() === trimmedOption.toLowerCase() &&
          !selectedOptions.includes(item)
      );
      if (existsInOther) {
        const existsIn = [];
        if (
          menCategories.some(
            (item) => item.toLowerCase() === trimmedOption.toLowerCase()
          )
        ) {
          existsIn.push("Men's");
        }
        if (
          womenCategories.some(
            (item) => item.toLowerCase() === trimmedOption.toLowerCase()
          )
        ) {
          existsIn.push("Women's");
        }
        if (
          bothOtherCategories.some(
            (item) => item.toLowerCase() === trimmedOption.toLowerCase()
          )
        ) {
          existsIn.push("Both/Other");
        }
        return `"${trimmedOption}" already exists in ${existsIn.join(
          " and "
        )} categories.`;
      }

      return null;
    },
    [
      selectedOptions,
      allSelectedCategories,
      menCategories,
      womenCategories,
      bothOtherCategories,
    ]
  );

  // Enhanced add option handler
  const handleAddOption = useCallback(() => {
    const newOption = inputValue.trim();

    const duplicateError = checkForDuplicates(newOption);
    if (duplicateError) {
      setErrorMessage(duplicateError);
      setTimeout(() => setErrorMessage(""), 5000);
      return;
    }

    // Add to available options if not already there
    setAvailableOptions((prev) => {
      const exists = prev.some(
        (item) => item.toLowerCase() === newOption.toLowerCase()
      );
      return exists ? prev : [...prev, newOption];
    });

    // Add to selected options
    setSelectedOptions((prev) => [...prev, newOption]);
    setInputValue("");
    setErrorMessage("");
  }, [inputValue, checkForDuplicates, setAvailableOptions, setSelectedOptions]);

  // Enhanced removal logic with cross-category validation
  const canRemoveCategory = useCallback(
    (option) => {
      // Count how many categories contain this option
      let categoryCount = 0;
      const existsIn = [];

      if (menCategories.includes(option)) {
        categoryCount++;
        existsIn.push("Men's");
      }
      if (womenCategories.includes(option)) {
        categoryCount++;
        existsIn.push("Women's");
      }
      if (bothOtherCategories.includes(option)) {
        categoryCount++;
        existsIn.push("Both/Other");
      }

      // If it exists in only one category, it cannot be removed
      if (categoryCount === 1) {
        return {
          canRemove: false,
          error: `"${option}" exists only in ${existsIn[0]} category. A category must exist in at least one section.`,
        };
      }

      return { canRemove: true, error: null };
    },
    [menCategories, womenCategories, bothOtherCategories]
  );

  // Enhanced remove selected option handler
  const removeSelectedOption = useCallback(
    (option) => {
      const { canRemove, error } = canRemoveCategory(option);

      if (!canRemove) {
        setErrorMessage(error);
        // Auto-clear error after 5 seconds
        setTimeout(() => setErrorMessage(""), 5000);
        return;
      }

      setErrorMessage("");
      setSelectedOptions((prev) => prev.filter((item) => item !== option));
    },
    [canRemoveCategory, setSelectedOptions]
  );

  // Handle key press
  const handleKeyPress = useCallback(
    (e) => {
      if (e.key === "Enter") {
        e.preventDefault();
        handleAddOption();
      }
    },
    [handleAddOption]
  );

  // Toggle option from dropdown
  const toggleOption = useCallback(
    (option) => {
      setSelectedOptions((prev) =>
        prev.includes(option)
          ? prev.filter((item) => item !== option)
          : [...prev, option]
      );
      setInputValue("");
      setIsDropdownOpen(false);
    },
    [setSelectedOptions]
  );

  // Filtered options for dropdown
  const filteredOptions = useMemo(() => {
    return availableOptions.filter(
      (option) =>
        option.toLowerCase().includes(inputValue.toLowerCase()) &&
        !selectedOptions.includes(option)
    );
  }, [availableOptions, inputValue, selectedOptions]);

  // Close dropdown handler
  const closeDropdown = useCallback(() => {
    setIsDropdownOpen(false);
  }, []);

  return (
    <div className="space-y-4">
      <div>
        <Typography variant="h6" color="blue-gray" className="mb-2">
          {title}
        </Typography>
        <Typography variant="small" color="gray" className="mb-4">
          {description}
        </Typography>
      </div>

      {/* Selected Options Display */}
      {selectedOptions.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {selectedOptions.map((option, index) => (
            <Chip
              key={`${option}-${index}`}
              value={option}
              onClose={() => removeSelectedOption(option)}
              className="bg-blue-500 text-white"
            />
          ))}
        </div>
      )}

      {/* Error Message Display */}
      {errorMessage && (
        <div className="rounded-lg p-3 mb-4">
          <Typography variant="small" className="text-red-600 font-medium">
            {errorMessage}
          </Typography>
        </div>
      )}

      {/* Input for adding/searching */}
      <div className="relative">
        <Input
          label="Add or search categories"
          value={inputValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          onFocus={() => setIsDropdownOpen(true)}
          className="mb-2"
        />
        {inputValue.trim() && (
          <Button
            size="sm"
            onClick={handleAddOption}
            className="absolute right-2 top-2 h-6 w-16 text-xs"
          >
            Add
          </Button>
        )}

        {/* Dropdown Options */}
        {isDropdownOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => (
                <div
                  key={`option-${option}-${index}`}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => toggleOption(option)}
                >
                  <Typography variant="small">{option}</Typography>
                </div>
              ))
            ) : (
              <div className="px-4 py-2 text-gray-500">
                <Typography variant="small">
                  {inputValue.trim()
                    ? "Press Enter to add new category"
                    : "No options available"}
                </Typography>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div className="fixed inset-0 z-5" onClick={closeDropdown} />
      )}
    </div>
  );
};

export const UpdateGlobalFiltersContainer = ({
  showAlert,
  useGetGlobalCategoriesQuery,
  useUpdateGlobalCategoriesMutation,
}) => {
  // RTK Query hooks
  const {
    data: categoriesData,
    isLoading: isLoadingCategories,
    error,
  } = useGetGlobalCategoriesQuery();
  const [updateGlobalCategories, { isLoading: isUpdating }] =
    useUpdateGlobalCategoriesMutation();

  // State for selected categories
  const [menCategories, setMenCategories] = useState([]);
  const [womenCategories, setWomenCategories] = useState([]);
  const [bothOtherCategories, setBothOtherCategories] = useState([]);

  // Predefined options
  const predefinedOptions = useMemo(
    () => ({
      men: [
        "Clothing",
        "Shoes",
        "Accessories",
        "Watches",
        "Grooming",
        "Sports",
        "Electronics",
      ],
      women: [
        "Clothing",
        "Shoes",
        "Accessories",
        "Jewelry",
        "Beauty",
        "Handbags",
        "Sports",
        "Electronics",
      ],
      other: [
        "Home & Garden",
        "Books",
        "Electronics",
        "Health",
        "Automotive",
        "Toys",
        "Pet Supplies",
      ],
    }),
    []
  );

  // State for all available options
  const [menOptions, setMenOptions] = useState(predefinedOptions.men);
  const [womenOptions, setWomenOptions] = useState(predefinedOptions.women);
  const [bothOtherOptions, setBothOtherOptions] = useState(
    predefinedOptions.other
  );

  // Load data from API on component mount
  useEffect(() => {
    if (categoriesData?.success && categoriesData?.data) {
      const { menCategory: apiMen = [], womenCategory: apiWomen = [] } =
        categoriesData.data;

      setMenCategories(apiMen);
      setWomenCategories(apiWomen);
      setBothOtherCategories([]);

      // Merge API data with predefined options
      setMenOptions((prev) => [...new Set([...prev, ...apiMen])]);
      setWomenOptions((prev) => [...new Set([...prev, ...apiWomen])]);
    }
  }, [categoriesData]);

  // Optimized save handler with better error handling
  const handleSaveChanges = useCallback(async () => {
    try {
      // Validation
      if (menCategories.length === 0 && womenCategories.length === 0) {
        showAlert(
          "error",
          "Please select at least one category for either Men's or Women's section."
        );
        return;
      }

      const updateData = {
        menCategory: menCategories,
        womenCategory: womenCategories,
        otherCategory: bothOtherCategories,
      };

      const result = await updateGlobalCategories(updateData).unwrap();

      if (result.success || result.statusCode === 200) {
        showAlert(
          "success",
          result.message || "Global filters updated successfully!"
        );
      } else {
        throw new Error(result.message || "Update failed");
      }
    } catch (err) {
      const errorMessage =
        err.data?.message ||
        err.message ||
        "Failed to update global filters. Please try again.";
      showAlert("error", errorMessage);
      console.error("Error saving filters:", err);
    }
  }, [
    menCategories,
    womenCategories,
    bothOtherCategories,
    updateGlobalCategories,
    showAlert,
  ]);

  // Loading state
  if (isLoadingCategories) {
    return (
      <div className="bg-transparent w-full">
        <Card className="w-full bg-white shadow-lg border border-gray-200">
          <CardBody className="p-8">
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <Typography variant="paragraph" color="gray">
                  Loading categories...
                </Typography>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-transparent w-full">
        <Card className="w-full bg-white shadow-lg border border-gray-200">
          <CardBody className="p-8">
            <div className="text-center">
              <Typography variant="h6" color="red" className="mb-2">
                Error Loading Categories
              </Typography>
              <Typography variant="small" color="gray">
                {error.message ||
                  "Failed to load categories. Please refresh the page."}
              </Typography>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="bg-transparent w-full">
      <Card className="w-full bg-white shadow-lg border border-gray-200">
        <CardBody className="p-8">
          <Typography variant="h5" color="blue-gray" className="mb-6">
            Update Global Product Category Filters
          </Typography>
          <Typography variant="paragraph" color="gray" className="mb-8">
            Configure product categories for different gender segments. These
            settings will determine which product categories are displayed for
            each target audience across the platform.
          </Typography>

          <div className="space-y-8">
            {/* Men Categories */}
            <div className="p-6 border border-gray-200 rounded-lg bg-blue-50/30">
              <MultiSelectWithAdd
                title="Men's Categories"
                description="Select product categories that will be shown for men's products"
                selectedOptions={menCategories}
                setSelectedOptions={setMenCategories}
                availableOptions={menOptions}
                setAvailableOptions={setMenOptions}
                categoryType="men"
                menCategories={menCategories}
                womenCategories={womenCategories}
                bothOtherCategories={bothOtherCategories}
                showAlert={showAlert}
              />
            </div>

            {/* Women Categories */}
            <div className="p-6 border border-gray-200 rounded-lg bg-pink-50/30">
              <MultiSelectWithAdd
                title="Women's Categories"
                description="Select product categories that will be shown for women's products"
                selectedOptions={womenCategories}
                setSelectedOptions={setWomenCategories}
                availableOptions={womenOptions}
                setAvailableOptions={setWomenOptions}
                categoryType="women"
                menCategories={menCategories}
                womenCategories={womenCategories}
                bothOtherCategories={bothOtherCategories}
                showAlert={showAlert}
              />
            </div>

            {/* Both/Other Categories */}
            <div className="p-6 border border-gray-200 rounded-lg bg-purple-50/30">
              <MultiSelectWithAdd
                title="Both/Other Categories"
                description="Select product categories that will be shown for all genders or unspecified gender products"
                selectedOptions={bothOtherCategories}
                setSelectedOptions={setBothOtherCategories}
                availableOptions={bothOtherOptions}
                setAvailableOptions={setBothOtherOptions}
                categoryType="other"
                menCategories={menCategories}
                womenCategories={womenCategories}
                bothOtherCategories={bothOtherCategories}
                showAlert={showAlert}
              />
            </div>
          </div>

          {/* Enhanced Configuration Summary */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg mt-8 mb-8 border border-gray-100">
            <Typography variant="h6" color="blue-gray" className="mb-4">
              Current Configuration Summary
            </Typography>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <Typography
                  variant="small"
                  className="font-semibold text-blue-600 mb-2 flex items-center gap-2"
                >
                  <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                  Men's Categories ({menCategories.length})
                </Typography>
                <Typography
                  variant="small"
                  color="gray"
                  className="leading-relaxed"
                >
                  {menCategories.length > 0
                    ? menCategories.join(", ")
                    : "None selected"}
                </Typography>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <Typography
                  variant="small"
                  className="font-semibold text-pink-600 mb-2 flex items-center gap-2"
                >
                  <span className="w-3 h-3 bg-pink-500 rounded-full"></span>
                  Women's Categories ({womenCategories.length})
                </Typography>
                <Typography
                  variant="small"
                  color="gray"
                  className="leading-relaxed"
                >
                  {womenCategories.length > 0
                    ? womenCategories.join(", ")
                    : "None selected"}
                </Typography>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <Typography
                  variant="small"
                  className="font-semibold text-purple-600 mb-2 flex items-center gap-2"
                >
                  <span className="w-3 h-3 bg-purple-500 rounded-full"></span>
                  Both/Other Categories ({bothOtherCategories.length})
                </Typography>
                <Typography
                  variant="small"
                  color="gray"
                  className="leading-relaxed"
                >
                  {bothOtherCategories.length > 0
                    ? bothOtherCategories.join(", ")
                    : "None selected"}
                </Typography>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={handleSaveChanges}
              disabled={isUpdating}
              className="flex items-center gap-2 bg-pink-400 hover:bg-pink-500"
              size="lg"
            >
              {isUpdating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Saving Changes...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};
