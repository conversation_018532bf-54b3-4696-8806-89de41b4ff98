import { useLocation, <PERSON>, useNavigate } from "react-router-dom";
import {
  Navbar,
  <PERSON><PERSON><PERSON>,
  Bread<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MenuList,
  MenuItem,
  IconButton,
} from "@material-tailwind/react";
import { Bars3Icon, UserCircleIcon } from "@heroicons/react/24/solid";
import { HomeIcon, PowerIcon } from "@heroicons/react/24/outline";
import { setOpenSidenav, useMaterialTailwindController } from "@/context";
import { useDispatch } from "react-redux";
import { logoutAdmin } from "@/api/services/Auth/AuthSlice";
import { useLogoutMutation } from "@/api/services/Admin/AdminService";

export function DashboardNavbar() {
  const [controller, dispatch] = useMaterialTailwindController();
  const { fixedNavbar, openSidenav } = controller;
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const reduxDispatch = useDispatch();

  const pathSegments = pathname.split("/").filter((el) => el !== "");
  const layout = pathSegments[0] || "dashboard";
  const page = pathSegments[1] || "dashboard";

  const [logout] = useLogoutMutation();

  const handleSignOut = async () => {
    try {
      await logout().unwrap();
      reduxDispatch(logoutAdmin());
      navigate("/auth/sign-in");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <Navbar
      color={fixedNavbar ? "white" : "transparent"}
      className={`rounded-xl transition-all ${
        fixedNavbar
          ? "sticky top-4 z-40 py-3 shadow-md shadow-blue-gray-500/5"
          : "px-0 py-1"
      }`}
      fullWidth
      blurred={fixedNavbar}
    >
      <div className="flex flex-col-reverse justify-between gap-6 md:flex-row md:items-center">
        {/* Breadcrumbs */}
        <div className="capitalize">
          <Breadcrumbs
            className={`bg-transparent p-0 transition-all ${
              fixedNavbar ? "mt-1" : ""
            }`}
          >
            <Link to={`/${layout}`}>
              <Typography
                variant="small"
                color="blue-gray"
                className="font-normal opacity-50 transition-all hover:text-blue-500 hover:opacity-100"
              >
                {layout}
              </Typography>
            </Link>

            <Typography variant="small" color="pink" className="font-normal">
              {page}
            </Typography>
          </Breadcrumbs>

          <Typography variant="h6" color="pink">
            {page}
          </Typography>
        </div>

        <div className="flex justify-end items-center">
          {/* Open Side nav button */}
          <IconButton
            variant="text"
            color="blue-gray"
            className="grid xl:hidden mr-2"
            onClick={() => setOpenSidenav(dispatch, !openSidenav)}
          >
            <Bars3Icon strokeWidth={3} className="h-6 w-6 text-blue-gray-500" />
          </IconButton>

          {/* Profile Dropdown with Circle Icon */}
          <div className="flex items-center">
            <Menu placement="bottom-end">
              <MenuHandler>
                <IconButton
                  variant="text"
                  color="blue-gray"
                  className="rounded-full"
                >
                  <UserCircleIcon className="h-7 w-7 text-blue-gray-500" />
                </IconButton>
              </MenuHandler>
              <MenuList className="p-1">
                <MenuItem
                  onClick={() => navigate("/dashboard")}
                  className="flex items-center gap-2"
                >
                  <HomeIcon className="h-4 w-4" />
                  Dashboard
                </MenuItem>
                <MenuItem
                  onClick={handleSignOut}
                  className="flex items-center gap-2 text-red-500 hover:bg-red-100"
                >
                  <PowerIcon className="h-4 w-4" />
                  Sign Out
                </MenuItem>
              </MenuList>
            </Menu>
          </div>
        </div>
      </div>
    </Navbar>
  );
}

DashboardNavbar.displayName = "/src/widgets/layout/dashboard-navbar.jsx";

export default DashboardNavbar;
