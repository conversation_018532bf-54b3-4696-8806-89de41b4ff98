import React, { useState } from "react";
import { UpdateGlobalFiltersContainer } from "@/components/Global-Filter/UpdateGlobalFiltersContainer";
import { Alert, IconButton } from "@material-tailwind/react";
import { XMarkIcon } from "@heroicons/react/24/solid";
import {
  useGetGlobalCategoriesQuery,
  useUpdateGlobalCategoriesMutation,
} from "@/api/services/Global-Filters/GlobalFiltersService";
const GlobalFilterPage = () => {
  const [alert, setAlert] = useState({ show: false, type: "", message: "" });
  const handleCloseAlert = () => {
    setAlert({ show: false, type: "", message: "" });
  };
  const showAlert = (type, message) => {
    setAlert({ show: true, type, message });
    setTimeout(() => {
      handleCloseAlert();
    }, 3000);
  };
  return (
    <div className="py-5">
      {alert.show && (
        <div className="fixed top-6 left-1/2 -translate-x-1/2 z-50 w-[90%] max-w-md">
          <Alert
            color={alert.type === "success" ? "green" : "red"}
            animate={{
              mount: { y: 0 },
              unmount: { y: -100 },
            }}
            className="flex items-center gap-2"
          >
            <div className="flex-1">{alert.message}</div>
            <IconButton
              variant="text"
              color="white"
              size="sm"
              onClick={handleCloseAlert}
              className="!absolute right-2 top-2.5"
            >
              <XMarkIcon className="h-5 w-5" />
            </IconButton>
          </Alert>
        </div>
      )}
      <UpdateGlobalFiltersContainer
        showAlert={showAlert}
        useGetGlobalCategoriesQuery={useGetGlobalCategoriesQuery}
        useUpdateGlobalCategoriesMutation={useUpdateGlobalCategoriesMutation}
      />
    </div>
  );
};
export default GlobalFilterPage;
