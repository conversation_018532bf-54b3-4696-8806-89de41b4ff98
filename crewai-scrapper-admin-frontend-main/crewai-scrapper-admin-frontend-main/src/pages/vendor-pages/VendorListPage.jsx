import React, { useState } from "react";
import {
  Card,
  CardBody,
  Typography,
  Input,
  Button,
} from "@material-tailwind/react";
import { useGetVendorListQuery } from "@/api/services/Vendors/VendorsService";

const VendorListPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // You can adjust this number

  const { data, isLoading: loading, isError, error } = useGetVendorListQuery();

  const vendors = React.useMemo(() => {
    if (!data) return [];

    if (data.data && data.data.vendors && Array.isArray(data.data.vendors)) {
      return data.data.vendors;
    }

    if (Array.isArray(data)) {
      return data;
    } else if (data.vendors && Array.isArray(data.vendors)) {
      return data.vendors;
    } else if (data.data && Array.isArray(data.data)) {
      return data.data;
    }

    return [];
  }, [data]);

  const filteredVendors = vendors.filter(
    (vendor) =>
      vendor?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor?.websiteUrl?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination calculations
  const totalPages = Math.ceil(filteredVendors.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentVendors = filteredVendors.slice(startIndex, endIndex);

  // Reset to first page when search term changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handlePrevious = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      let startPage = Math.max(1, currentPage - 2);
      let endPage = Math.min(totalPages, currentPage + 2);

      if (currentPage <= 3) {
        endPage = maxVisiblePages;
      }

      if (currentPage >= totalPages - 2) {
        startPage = totalPages - maxVisiblePages + 1;
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }
    }

    return pageNumbers;
  };

  if (isError) {
    console.error("Error fetching vendors:", error);
  }

  return (
    <div className="py-5">
      <Card className="p-8 bg-white min-h-[80vh]">
        <div className="text-left mb-4">
          <Typography variant="h5" color="blue-gray" className="mb-2">
            Vendor List
          </Typography>
          <Typography variant="paragraph" color="gray" className="text-md">
            Browse and manage all vendors. Use the search bar to filter vendors
            by name or website URL.
          </Typography>
        </div>
        <div className="mb-4">
          <Input
            type="text"
            label="Search by name or website URL..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </div>
        <CardBody className="px-0 pt-0 pb-2 min-h-[60vh]">
          {loading ? (
            <Typography color="gray" className="text-center py-8">
              Loading vendors...
            </Typography>
          ) : isError ? (
            <Typography color="red" className="text-center py-8">
              Error loading vendors. Please try again.
            </Typography>
          ) : (
            <>
              <table className="w-full table-auto">
                <thead>
                  <tr>
                    {["#", "Name", "Website URL"].map((el) => (
                      <th
                        key={el}
                        className="border-b border-blue-gray-50 py-3 px-5 text-left"
                      >
                        <Typography
                          variant="small"
                          className="text-[11px] font-bold uppercase text-blue-gray-400"
                        >
                          {el}
                        </Typography>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {currentVendors.length > 0 ? (
                    currentVendors.map((vendor, index) => {
                      const globalIndex = startIndex + index + 1;
                      const className = `py-3 px-5 ${
                        index === currentVendors.length - 1
                          ? ""
                          : "border-b border-blue-gray-50"
                      }`;
                      return (
                        <tr key={vendor.id}>
                          <td className={className}>
                            <Typography className="text-xs font-semibold text-blue-gray-600">
                              {globalIndex}
                            </Typography>
                          </td>
                          <td className={className}>
                            <Typography
                              variant="small"
                              color="blue-gray"
                              className="font-semibold"
                            >
                              {vendor.name}
                            </Typography>
                          </td>
                          <td className={className}>
                            <Typography className="text-xs font-semibold text-blue-gray-600">
                              {vendor.websiteUrl ? (
                                <a
                                  href={vendor.websiteUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-500 hover:underline"
                                >
                                  {vendor.websiteUrl}
                                </a>
                              ) : (
                                <span className="text-red-500">N/A</span>
                              )}
                            </Typography>
                          </td>
                        </tr>
                      );
                    })
                  ) : (
                    <tr>
                      <td colSpan={3} className="text-center py-8">
                        <Typography variant="h6" color="blue-gray">
                          No Vendors Found
                        </Typography>
                        <Typography
                          variant="small"
                          color="blue-gray"
                          className="mb-4"
                        >
                          Try adjusting your search or check back later.
                        </Typography>
                        <div className="flex justify-center icon-link w-full">
                          <Button
                            variant="outlined"
                            size="sm"
                            onClick={() => {
                              setSearchTerm("");
                              setCurrentPage(1);
                            }}
                            className="flex items-center gap-1"
                          >
                            Clear Search
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>

              {/* Pagination Controls */}
              {filteredVendors.length > 0 && totalPages > 1 && (
                <div className="flex items-center justify-between border-t border-blue-gray-50 pt-4 mt-4">
                  <div className="flex items-center gap-2">
                    <Typography variant="small" color="blue-gray">
                      Showing {startIndex + 1} to{" "}
                      {Math.min(endIndex, filteredVendors.length)} of{" "}
                      {filteredVendors.length} entries
                    </Typography>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outlined"
                      size="sm"
                      onClick={handlePrevious}
                      disabled={currentPage === 1}
                      className="flex items-center gap-1"
                    >
                      Previous
                    </Button>

                    {getPageNumbers().map((pageNumber) => (
                      <Button
                        key={pageNumber}
                        variant={
                          currentPage === pageNumber ? "filled" : "outlined"
                        }
                        size="sm"
                        onClick={() => handlePageChange(pageNumber)}
                        className="min-w-[40px]"
                      >
                        {pageNumber}
                      </Button>
                    ))}

                    <Button
                      variant="outlined"
                      size="sm"
                      onClick={handleNext}
                      disabled={currentPage === totalPages}
                      className="flex items-center gap-1"
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default VendorListPage;
