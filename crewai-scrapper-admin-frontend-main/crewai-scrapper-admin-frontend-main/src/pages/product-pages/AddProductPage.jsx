import React, { useState } from "react";
import { AddSingleProductContainer } from "@/components/Products/AddSingleProductContainer";
import { BulkProductUploadContainer } from "@/components/Products/BulkProductUploadContainer";
import {
  <PERSON><PERSON>,
  IconButton,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel,
} from "@material-tailwind/react";
import { XMarkIcon } from "@heroicons/react/24/solid";

const AddProductPage = () => {
  const [alert, setAlert] = useState({ show: false, type: "", message: "" });
  const [activeTab, setActiveTab] = useState("single");

  const handleCloseAlert = () => {
    setAlert({ show: false, type: "", message: "" });
  };

  const showAlert = (type, message) => {
    setAlert({ show: true, type, message });
    setTimeout(() => {
      handleCloseAlert();
    }, 3000);
  };

  const handleAddProduct = (newProduct) => {
    showAlert("success", "Product added successfully!");
  };

  return (
    <div className="py-5">
      {alert.show && (
        <div className="fixed top-6 left-1/2 -translate-x-1/2 z-50 w-[90%] max-w-md">
          <Alert
            color={alert.type === "success" ? "green" : "red"}
            animate={{
              mount: { y: 0 },
              unmount: { y: -100 },
            }}
            className="flex items-center gap-2"
          >
            <div className="flex-1">{alert.message}</div>
            <IconButton
              variant="text"
              color="white"
              size="sm"
              onClick={handleCloseAlert}
              className="!absolute right-2 top-2.5"
            >
              <XMarkIcon className="h-5 w-5" />
            </IconButton>
          </Alert>
        </div>
      )}
      <Tabs value={activeTab} onChange={setActiveTab} className="w-full">
        <TabsHeader
          className="rounded-lg border border-blue-gray-50 bg-blue-gray-50 p-1 mb-6"
          indicatorProps={{
            className: "bg-white shadow-none rounded-md !translate-x-0",
          }}
        >
          <Tab
            value="single"
            className="text-gray-700 font-medium focus:text-pink-500 flex items-center"
          >
            Single Product Upload
          </Tab>
          <Tab
            value="bulk"
            className="text-gray-700 font-medium focus:text-pink-500 flex items-center"
          >
            Bulk Product Upload
          </Tab>
        </TabsHeader>
        <TabsBody>
          <TabPanel value="single">
            <AddSingleProductContainer onProductAdded={handleAddProduct} />
          </TabPanel>
          <TabPanel value="bulk">
            <BulkProductUploadContainer />
          </TabPanel>
        </TabsBody>
      </Tabs>
    </div>
  );
};

export default AddProductPage;
