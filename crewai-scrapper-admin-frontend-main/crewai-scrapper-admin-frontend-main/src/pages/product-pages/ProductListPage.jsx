import React, { useEffect, useState } from "react";
import { AllProductsContainer } from "@/components/Products/AllProductsListContainer";
import { useAllProductsQuery } from "@/api/services/Products/ProductsService";

const ProductListPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [productsList, setProductsList] = useState([]);
  const [productsPagination, setProductsPagination] = useState(null);
  const limit = 10;

  const {
    data: productsData,
    isLoading,
    error,
  } = useAllProductsQuery({
    page: currentPage,
    limit,
    searchTerm,
  });

  useEffect(() => {
    if (productsData?.data?.data) {
      setProductsList(productsData.data.data);
    }

    if (productsData?.data?.pagination) {
      setProductsPagination(productsData.data.pagination);
    }
  }, [productsData]);

  return (
    <div className="py-5">
      <AllProductsContainer
        data={productsList}
        pagination={productsPagination}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        isLoading={isLoading}
        error={error}
      />
    </div>
  );
};

export default ProductListPage;
