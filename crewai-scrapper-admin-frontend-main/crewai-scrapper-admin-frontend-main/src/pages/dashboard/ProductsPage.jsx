// import React, { useEffect, useState } from "react";
// import {
//   Tabs,
//   <PERSON><PERSON>Header,
//   TabsBody,
//   Tab,
//   TabPanel,
//   <PERSON><PERSON>,
//   IconButton,
// } from "@material-tailwind/react";
// import {
//   AddProductContainer,
//   AllProductsContainer,
// } from "@/components/Products/index";
// import { UpdateGlobalFiltersContainer } from "@/components/GlobalFilters/index";
// import {
//   ShoppingBagIcon,
//   PlusCircleIcon,
//   AdjustmentsHorizontalIcon,
// } from "@heroicons/react/24/outline";
// import { XMarkIcon } from "@heroicons/react/24/solid";
// import { useAllProductsQuery } from "@/api/services/Products/ProductsService";

// const ProductsPage = () => {
//   const [activeTab, setActiveTab] = useState("allProducts");
//   const [alert, setAlert] = useState({ show: false, type: "", message: "" });

//   const [currentPage, setCurrentPage] = useState(1);
//   const [searchTerm, setSearchTerm] = useState("");
//   const [productsList, setProductsList] = useState([]);
//   const [productsPagination, setProductsPagination] = useState(null);
//   const limit = 10;

//   const handleCloseAlert = () => {
//     setAlert({ show: false, type: "", message: "" });
//   };

//   const showAlert = (type, message) => {
//     setAlert({ show: true, type, message });
//     setTimeout(() => {
//       handleCloseAlert();
//     }, 3000); // Hide the alert after 3 seconds
//   };

//   const {
//     data: productsData,
//     isLoading,
//     error,
//   } = useAllProductsQuery({
//     page: currentPage,
//     limit,
//     searchTerm,
//   });

//   useEffect(() => {
//     if (productsData?.data?.data && productsData?.data?.pagination) {
//       setProductsList(productsData.data.data);
//       setProductsPagination(productsData.data.pagination);
//     }
//   }, [productsData]);

//   const handleAddProduct = (newProduct) => {
//     setProductsList((prevProducts) => [newProduct, ...prevProducts]);

//     showAlert("success", "Product added successfully!");
//   };

//   const data = [
//     {
//       label: "All Products",
//       value: "allProducts",
//       icon: <ShoppingBagIcon className="w-5 h-5 mr-2" />,
//       component: (
//         <AllProductsContainer
//           data={productsList}
//           pagination={productsPagination}
//           searchTerm={searchTerm}
//           setSearchTerm={setSearchTerm}
//           currentPage={currentPage}
//           setCurrentPage={setCurrentPage}
//           isLoading={isLoading}
//           error={error}
//         />
//       ),
//     },
//     {
//       label: "Add Products",
//       value: "addProducts",
//       icon: <PlusCircleIcon className="w-5 h-5 mr-2" />,
//       component: (
//         <AddProductContainer
//           onProductAdded={handleAddProduct} // Pass the handler as prop
//         />
//       ),
//     },
//     {
//       label: "Update Global Filter",
//       value: "updateFilter",
//       icon: <AdjustmentsHorizontalIcon className="w-5 h-5 mr-2" />,
//       component: <UpdateGlobalFiltersContainer showAlert={showAlert} />,
//     },
//   ];

//   return (
//     <div className="py-5">
//       {alert.show && (
//         <div className="fixed top-6 left-1/2 -translate-x-1/2 z-50 w-[90%] max-w-md">
//           <Alert
//             color={alert.type === "success" ? "green" : "red"}
//             animate={{
//               mount: { y: 0 },
//               unmount: { y: -100 },
//             }}
//             className="flex items-center gap-2"
//           >
//             <div className="flex-1">{alert.message}</div>
//             <IconButton
//               variant="text"
//               color="white"
//               size="sm"
//               onClick={handleCloseAlert}
//               className="!absolute right-2 top-2.5"
//             >
//               <XMarkIcon className="h-5 w-5" />
//             </IconButton>
//           </Alert>
//         </div>
//       )}
//       <Tabs value={activeTab}>
//         <TabsHeader
//           className="rounded-lg border border-blue-gray-50 bg-blue-gray-50 p-1"
//           indicatorProps={{
//             className: "bg-white shadow-none rounded-md !translate-x-0",
//           }}
//         >
//           {data.map(({ label, value, icon }) => (
//             <Tab
//               key={value}
//               value={value}
//               className="text-gray-700 font-medium focus:text-pink-500 flex items-center"
//             >
//               <div className="flex justify-center items-center">
//                 {icon} <span>{label}</span>
//               </div>
//             </Tab>
//           ))}
//         </TabsHeader>

//         <TabsBody
//           animate={{
//             mount: { y: 0, x: 0, z: 0, transition: { duration: 0.1 } },
//             unmount: { y: 0, x: 0, z: 0, transition: { duration: 0.1 } },
//           }}
//         >
//           {data.map(({ value, component }) => (
//             <TabPanel key={value} value={value}>
//               {component}
//             </TabPanel>
//           ))}
//         </TabsBody>
//       </Tabs>
//     </div>
//   );
// };

// export default ProductsPage;
