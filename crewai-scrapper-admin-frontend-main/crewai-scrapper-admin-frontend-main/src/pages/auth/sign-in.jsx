import { useState, useEffect } from "react";
import { useLoginMutation } from "@/api/services/Auth/AuthService";
import { useDispatch } from "react-redux";
import { setCredentials } from "@/api/services/Auth/AuthSlice";
import { useNavigate, useLocation } from "react-router-dom";
import { AuthPageImage } from "@/components/auth";
import {
  Input,
  Button,
  Typography,
  Alert,
  IconButton,
} from "@material-tailwind/react";
import { XMarkIcon, EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";

export function SignIn() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [login, { isLoading }] = useLoginMutation();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [showAlert, setShowAlert] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);

  // Auto-dismiss alert after 4 seconds
  useEffect(() => {
    let timer;
    if (showAlert) {
      timer = setTimeout(() => {
        setShowAlert(false);
        setErrorMessage("");
      }, 4000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [showAlert]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage("");
    setFieldErrors({});
    setShowAlert(false);

    const errors = {};
    if (!email.trim()) errors.email = "Email is required";
    if (!password.trim()) errors.password = "Password is required";

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      return;
    }

    try {
      const res = await login({ email, password }).unwrap();

      if (res?.data?.user?.role?.value !== "admin") {
        setErrorMessage("Access denied. You are not an admin.");
        setShowAlert(true);
        setEmail("");
        setPassword("");
        return;
      }

      // Pass the entire response data
      dispatch(setCredentials(res.data));
      setEmail("");
      setPassword("");
      const from = location.state?.from?.pathname || "/dashboard/home";
      navigate(from, { replace: true });
    } catch (err) {
      setErrorMessage(err?.data?.message || "Invalid credentials");
      setShowAlert(true);
    }
  };

  const handleCloseAlert = () => {
    setShowAlert(false);
    setErrorMessage("");
  };

  return (
    <section className="p-8 flex gap-5 h-screen relative">
      <AuthPageImage />

      {showAlert && errorMessage && (
        <div className="absolute top-6 left-1/2 -translate-x-1/2 z-50 w-[90%] max-w-md">
          <Alert
            color="red"
            animate={{
              mount: { y: 0 },
              unmount: { y: -100 },
            }}
            className="flex items-center gap-2"
          >
            <div className="flex-1">{errorMessage}</div>
            <IconButton
              variant="text"
              color="white"
              size="sm"
              onClick={handleCloseAlert}
              className="!absolute right-2 top-2.5"
            >
              <XMarkIcon className="h-5 w-5" />
            </IconButton>
          </Alert>
        </div>
      )}

      <div className="w-full h-full lg:w-[30%] px-10 flex flex-col justify-center items-center">
        <div className="text-center">
          <img
            src="/app-img/large-logo.svg"
            alt="logo"
            className="h-12 w-auto"
          />
        </div>

        <form
          onSubmit={handleSubmit}
          className="mt-8 mb-2 mx-auto w-80 max-w-screen-lg lg:w-[100%]"
        >
          <div className="mb-1 flex flex-col gap-4">
            <div>
              <Input
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  setFieldErrors((prev) => ({ ...prev, email: "" }));
                  setErrorMessage("");
                  setShowAlert(false);
                }}
                size="lg"
                label="Enter email"
                error={!!fieldErrors.email}
              />
              {fieldErrors.email && (
                <Typography variant="small" color="red" className="mt-1 ml-1">
                  {fieldErrors.email}
                </Typography>
              )}
            </div>

            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  setFieldErrors((prev) => ({ ...prev, password: "" }));
                  setErrorMessage("");
                  setShowAlert(false);
                }}
                size="lg"
                label="Enter password"
                error={!!fieldErrors.password}
                className="pr-12" // <== add right padding for the icon space
              />

              {/* Eye Icon absolutely placed inside the input field */}
              <div
                className="absolute top-1/2 right-3 transform -translate-y-1/2 cursor-pointer text-gray-500"
                onClick={() => setShowPassword((prev) => !prev)}
              >
                {showPassword ? (
                  <EyeIcon className="h-5 w-5" />
                ) : (
                  <EyeSlashIcon className="h-5 w-5" />
                )}
              </div>
            </div>
          </div>

          <Button
            type="submit"
            className="mt-6"
            fullWidth
            color="pink"
            disabled={isLoading}
          >
            {isLoading ? "Signing In..." : "Sign In"}
          </Button>
        </form>
      </div>
    </section>
  );
}

export default SignIn;
