import {
  HomeIcon,
  ServerStackIcon,
  PlusCircleIcon,
  AdjustmentsHorizontalIcon,
  BuildingOffice2Icon,
  UserPlusIcon,
  ListBulletIcon,
} from "@heroicons/react/24/solid";
import { Home } from "@/pages/dashboard";
import { SignIn } from "@/pages/auth";
import ProductListPage from "./pages/product-pages/ProductListPage";
import AddProductPage from "./pages/product-pages/AddProductPage";
import GlobalFilterPage from "./pages/global-filter-pages/GlobalFilterPage";
import VendorListPage from "./pages/vendor-pages/VendorListPage";
import AddVendorPage from "./pages/vendor-pages/AddVendorPage";

const icon = {
  className: "w-5 h-5 text-inherit",
};

export const routes = [
  {
    layout: "dashboard",
    pages: [
      {
        icon: <HomeIcon {...icon} />,
        name: "dashboard",
        path: "/home",
        element: <Home />,
        showInSidebar: true,
      },

      {
        icon: <ListBulletIcon {...icon} />,
        name: "List Products",
        path: "/list-product",
        element: <ProductListPage />,
        showInSidebar: true,
      },
      {
        icon: <PlusCircleIcon {...icon} />,
        name: "Add Products",
        path: "/add-products",
        element: <AddProductPage />,
        showInSidebar: true,
      },
      {
        icon: <AdjustmentsHorizontalIcon {...icon} />,
        name: "Set Global Filter",
        path: "/global-filter",
        element: <GlobalFilterPage />,
        showInSidebar: true,
      },
      {
        icon: <BuildingOffice2Icon {...icon} />,
        name: "List Vendors",
        path: "/vendor-list",
        element: <VendorListPage />,
        showInSidebar: true,
      },
      {
        icon: <UserPlusIcon {...icon} />,
        name: "Add Vendors",
        path: "/add-vendor",
        element: <AddVendorPage />,
        showInSidebar: true,
      },
    ],
  },
  {
    title: "auth pages",
    layout: "auth",
    pages: [
      {
        icon: <ServerStackIcon {...icon} />,
        name: "sign in",
        path: "/sign-in",
        element: <SignIn />,
        showInSidebar: true,
      },
    ],
  },
];

export default routes;
