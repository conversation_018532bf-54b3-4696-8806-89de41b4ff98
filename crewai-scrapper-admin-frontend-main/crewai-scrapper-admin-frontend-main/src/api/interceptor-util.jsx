import { Mutex } from "async-mutex";
import { setCredentials, logoutAdmin } from "./services/Auth/AuthSlice";
import { baseQuery } from "./api-utils";

const mutex = new Mutex();

export const baseQueryWithReauth = async (args, api, extraOptions) => {
  await mutex.waitForUnlock();

  let result = await baseQuery(args, api, extraOptions);

  if (result.error && result.error.status === 401) {
    if (!mutex.isLocked()) {
      const release = await mutex.acquire();

      try {
        const accessToken = localStorage.getItem("accessToken") || "";

        const refreshResult = await baseQuery(
          {
            url: "refresh-token",
            method: "GET",
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          },
          api,
          extraOptions
        );

        if (refreshResult.data) {
          localStorage.setItem(
            "accessToken",
            refreshResult.data.data.accessToken || ""
          );

          result = await baseQuery(
            {
              ...args,
              headers: {
                Authorization: `Bearer ${refreshResult.data.accessToken}`,
              },
            },
            api,
            extraOptions
          );
        } else {
          api.dispatch(logoutAdmin());
        }
      } catch (error) {
        console.error("Token refresh failed:", error);
        api.dispatch(logoutAdmin());
      } finally {
        release();
      }
    } else {
      await mutex.waitForUnlock();

      const accessToken = localStorage.getItem("accessToken") || "";
      result = await baseQuery(
        {
          ...args,
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
        api,
        extraOptions
      );
    }
  }

  return result;
};
