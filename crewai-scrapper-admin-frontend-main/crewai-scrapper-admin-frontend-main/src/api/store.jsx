import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./services/Auth/AuthSlice";
import { authApi } from "./services/Auth/AuthService";
import { allProductsApi } from "./services/Products/ProductsService";
import { adminApi } from "./services/Admin/AdminService";
import { vendorApi } from "./services/Vendors/VendorsService";
import { globalFiltersApi } from "./services/Global-Filters/GlobalFiltersService";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    [authApi.reducerPath]: authApi.reducer,
    [adminApi.reducerPath]: adminApi.reducer,
    [globalFiltersApi.reducerPath]: globalFiltersApi.reducer,
    [allProductsApi.reducerPath]: allProductsApi.reducer,
    [vendorApi.reducerPath]: vendorApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      allProductsApi.middleware,
      globalFiltersApi.middleware,
      adminApi.middleware,
      vendorApi.middleware
    ),
});
