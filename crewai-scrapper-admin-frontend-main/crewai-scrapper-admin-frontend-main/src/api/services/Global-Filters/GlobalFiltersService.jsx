import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../../interceptor-util";
export const globalFiltersApi = createApi({
  reducerPath: "globalFiltersApi",
  keepUnusedDataFor: 0,
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    //   ----------------------------- Global Filters -----------------------------
    getGlobalCategories: builder.query({
      query: () => ({
        url: "/admin/global-categories",
        method: "GET",
      }),
    }),
    updateGlobalCategories: builder.mutation({
      query: (body) => ({
        url: "/admin/global-categories",
        method: "PUT",
        body,
      }),
    }),
  }),
});
export const {
  useGetGlobalFilterQuery,
  useUpdateGlobalFilterMutation,
  useGetGlobalCategoriesQuery,
  useUpdateGlobalCategoriesMutation,
} = globalFiltersApi;
