import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../../interceptor-util";

export const vendorApi = createApi({
  reducerPath: "vendorApi",
  keepUnusedDataFor: 0,
  baseQuery: baseQueryWithReauth,

  endpoints: (builder) => ({
    getVendorList: builder.query({
      query: () => ({
        url: `/admin/vendors`,
        method: "GET",
      }),
    }),

    addSingleVendor: builder.mutation({
      query: (body) => {
        return {
          url: "/admin/vendors/single",
          method: "POST",
          body,
        };
      },
    }),

    addBulkVendors: builder.mutation({
      query: (body) => {
        return {
          url: "/admin/vendors/bulk",
          method: "POST",
          body,
        };
      },
    }),
  }),
});

export const {
  useGetVendorListQuery,
  useAddSingleVendorMutation,
  useAddBulkVendorsMutation,
} = vendorApi;
