import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../../interceptor-util";

export const allProductsApi = createApi({
  reducerPath: "allProductsApi",
  keepUnusedDataFor: 0,

  baseQuery: baseQueryWithReauth,

  endpoints: (builder) => ({
    // ----------------------------- Products -----------------------------
    allProducts: builder.query({
      query: ({ page = 1, limit = 10, searchTerm = "" }) => ({
        url: `/admin/products?page=${page}&limit=${limit}&searchTerm=${searchTerm}`,
        method: "GET",
      }),
    }),

    addSingleProduct: builder.mutation({
      query: (body) => {
        return {
          url: "/admin/products/single",
          method: "POST",
          body,
        };
      },
    }),

    addBulkProducts: builder.mutation({
      query: (body) => {
        return {
          url: "/admin/products/bulk",
          method: "POST",
          body,
        };
      },
    }),
  }),
});

export const {
  useAllProductsQuery,
  useAddSingleProductMutation,
  useAddBulkProductsMutation,
} = allProductsApi;
