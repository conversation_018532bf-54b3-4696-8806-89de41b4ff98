import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../../interceptor-util";

export const adminApi = createApi({
  reducerPath: "adminApi",
  keepUnusedDataFor: 0,
  baseQuery: baseQueryWithReauth,

  endpoints: (builder) => ({
    logout: builder.mutation({
      query: () => ({
        url: `/logout`,
        method: "POST",
      }),
    }),
  }),
});

export const { useLogoutMutation } = adminApi;
