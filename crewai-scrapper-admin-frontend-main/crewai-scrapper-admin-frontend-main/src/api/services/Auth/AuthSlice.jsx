import { createSlice } from "@reduxjs/toolkit";

const getAdminFromStorage = () => {
  try {
    const adminData = localStorage.getItem("admin");
    if (!adminData || adminData === "undefined") {
      return null;
    }
    return JSON.parse(adminData);
  } catch (error) {
    console.error("Error parsing admin data from localStorage:", error);
    return null;
  }
};

const admin = getAdminFromStorage();
const accessToken = localStorage.getItem("accessToken") || null;

const initialState = {
  admin,
  accessToken,
  isAuthenticated: !!admin && !!accessToken,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setCredentials: (state, action) => {
      const { user, accessToken } = action.payload;
      state.admin = user;
      state.accessToken = accessToken;
      localStorage.setItem("admin", JSON.stringify(user));
      localStorage.setItem("accessToken", accessToken);
      state.isAuthenticated = true;
    },

    logoutAdmin: (state) => {
      state.admin = null;
      state.accessToken = null;
      state.isAuthenticated = false;
      localStorage.clear();
    },
  },
});

export const { setCredentials, logoutAdmin } = authSlice.actions;
export default authSlice.reducer;
