import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "../../api-utils";

export const authApi = createApi({
  reducerPath: "authApi",
  keepUnusedDataFor: 0,
  baseQuery,

  endpoints: (builder) => ({
    login: builder.mutation({
      query: (credentials) => ({
        url: "login",
        method: "POST",
        body: credentials,
      }),
    }),
  }),
});

export const { useLoginMutation } = authApi;
