// Test script for public AI search enhancement
import axios from 'axios';

const BASE_URL = 'http://localhost:4000/api';

async function testPublicAI() {
  try {
    console.log('🧪 Testing Public AI Search Enhancement...\n');

    const testSearchTerms = [
      'baggy jeans',
      'I need running shoes',
      'looking for a dress',
      'want gaming laptop',
      'cheap phone under 200'
    ];

    for (const searchTerm of testSearchTerms) {
      console.log(`\n🔍 Testing: "${searchTerm}"`);
      
      try {
        const response = await axios.post(`${BASE_URL}/public/enhance-search`, {
          searchTerm: searchTerm
        });

        const data = response.data.data;
        console.log(`✅ Original: "${data.originalTerm}"`);
        console.log(`✨ Enhanced: "${data.enhancedTerm}"`);
        console.log(`💬 Suggestion: "${data.suggestion}"`);
        
        if (data.aiEnhanced === false) {
          console.log(`⚠️  AI enhancement was not available (fallback used)`);
        }
        
      } catch (error) {
        console.log(`❌ Failed: ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('\n🎉 Public AI test completed!');

  } catch (error) {
    console.error('\n❌ Test setup failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Tip: Make sure the server is running on port 4000');
      console.log('   Run: npm start in the backend directory');
    }
  }
}

// Run the test
testPublicAI();
