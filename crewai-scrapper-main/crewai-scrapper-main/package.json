{"name": "scrapping-backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"@fast-csv/format": "^5.0.2", "apify-client": "^2.12.5", "axios": "^1.9.0", "bcrypt": "^6.0.0", "cheerio": "^1.0.0-rc.12", "cloudinary": "^2.7.0", "cors": "^2.8.5", "crewai-js": "^0.0.1", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "fs": "^0.0.1-security", "google-auth-library": "^10.1.0", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "nodemailer": "^7.0.3", "openai": "^5.7.0", "pg": "^8.16.0", "puppeteer": "^21.6.1", "reflect-metadata": "^0.2.2", "resend": "^4.5.1", "streamifier": "^0.1.1", "typeorm": "^0.3.24", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["crewai", "scraping", "alibaba", "api", "nodejs", "expressjs"], "author": "", "license": "ISC", "description": ""}