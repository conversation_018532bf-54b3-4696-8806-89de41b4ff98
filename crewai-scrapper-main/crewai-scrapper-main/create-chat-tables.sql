-- SQL script to manually create chat tables if needed
-- Run this if DB_SYNCHRONIZE=false or if you prefer manual table creation

-- Create conversations table
CREATE TABLE IF NOT EXISTS conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255),
    "initialSearchTerm" TEXT NOT NULL,
    "lastSearchTerm" TEXT,
    context JSONB,
    "isActive" BOOLEAN DEFAULT true,
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "userId" UUID NOT NULL,
    FOREIGN KEY ("userId") REFERENCES users(id) ON DELETE CASCADE
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content TEXT NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    "searchTermGenerated" TEXT,
    metadata JSONB,
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "conversationId" UUID NOT NULL,
    FOREIGN KEY ("conversationId") REFERENCES conversations(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations("userId");
CREATE INDEX IF NOT EXISTS idx_conversations_active ON conversations("isActive");
CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations("updatedAt");
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages("conversationId");
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages("createdAt");
CREATE INDEX IF NOT EXISTS idx_messages_role ON messages(role);

-- Add comments for documentation
COMMENT ON TABLE conversations IS 'Stores AI chat conversations with search context';
COMMENT ON TABLE messages IS 'Stores individual messages within conversations';
COMMENT ON COLUMN conversations."initialSearchTerm" IS 'The first search term that started the conversation';
COMMENT ON COLUMN conversations."lastSearchTerm" IS 'The most recent search term generated';
COMMENT ON COLUMN conversations.context IS 'JSON context including search history and preferences';
COMMENT ON COLUMN messages."searchTermGenerated" IS 'Search term generated by AI from this message';
COMMENT ON COLUMN messages.metadata IS 'Additional metadata like confidence scores, timestamps, etc.';

-- Verify tables were created
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('conversations', 'messages')
ORDER BY table_name, ordinal_position;
