# AI Chat Assistant Setup Guide

## 🚀 Quick Setup

### 1. Environment Variables

Make sure your `.env` file includes the OpenAI API key:

```env
# Existing database and other configs...
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_DATABASE=your_database
DB_SYNCHRONIZE=true

# Add this for AI chat functionality
OPENAI_API_KEY=your_openai_api_key_here
```

### 2. Get OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in
3. Navigate to API Keys section
4. Create a new API key
5. Copy and add it to your `.env` file

### 3. Database Setup

The chat tables will be automatically created when you start the server (if `DB_SYNCHRONIZE=true`).

**New Tables Created:**
- `conversations` - Stores chat conversations
- `messages` - Stores individual messages

### 4. Start the Server

```bash
cd crewai-scrapper-main/crewai-scrapper-main
npm install
npm start
```

### 5. Test the Chat Endpoints

Run the test script to verify everything is working:

```bash
node test-chat-endpoints.js
```

## 🎯 How It Works

### Frontend Integration

The frontend now includes:
- **Chat Interface**: Sidebar chat with conversation history
- **AI Search Enhancement**: Smart search suggestions and query optimization
- **Accio-like Flow**: Search from landing page opens chat automatically

### Backend API Endpoints

```
POST /api/chat/conversations
POST /api/chat/conversations/:id/messages
GET  /api/chat/conversations/:id
GET  /api/chat/conversations
DELETE /api/chat/conversations/:id
POST /api/scraping/chat-search
```

### AI Features

1. **Context Preservation**: Maintains conversation history and context
2. **Search Term Generation**: Converts natural language to optimized search queries
3. **Smart Suggestions**: Provides search suggestions based on conversation context
4. **Intent Recognition**: Understands search, refine, filter, and compare intents

## 🔧 Troubleshooting

### Common Issues

**1. "OpenAI API Key not found"**
- Make sure `OPENAI_API_KEY` is set in your `.env` file
- Restart the server after adding the key

**2. "Database connection failed"**
- Check your database credentials in `.env`
- Ensure PostgreSQL is running
- Verify database exists

**3. "Chat tables not found"**
- Set `DB_SYNCHRONIZE=true` in `.env` to auto-create tables
- Or run database migrations if you have them

**4. "Import/Export errors"**
- Make sure all dependencies are installed: `npm install`
- Check Node.js version (requires Node 16+)

### Testing Without OpenAI

If you want to test without OpenAI (for development), you can modify the AI service to return mock responses:

```javascript
// In src/third-party/OpenAI/aiChatService.js
export const generateSearchTermFromConversation = async (messages, userMessage) => {
  // Mock response for testing
  return {
    searchTerm: userMessage.substring(0, 50),
    response: "I'll help you find products related to your request."
  };
};
```

## 🎨 Frontend Usage

### Basic Chat Flow

1. User searches from landing page
2. Chat opens automatically (Accio-style)
3. AI processes the search and shows products
4. User can continue conversation to refine search
5. Context is maintained throughout the session

### Key Components

- `ChatInterface` - Main chat sidebar
- `ChatToggle` - Floating chat button
- `MessageList` - Message display with typing indicators
- `ConversationList` - Conversation history

### Redux State

Chat state is managed in Redux with:
- Active conversation tracking
- Message history
- UI state (open/closed, typing indicators)
- Search integration

## 📱 Mobile Responsiveness

The chat interface is responsive and works on:
- Desktop: Sidebar layout
- Tablet: Collapsible sidebar
- Mobile: Full-screen overlay

## 🔮 Future Enhancements

Potential improvements:
- Voice input/output
- Product recommendations based on chat history
- Multi-language support
- Advanced filtering through natural language
- Integration with more AI models

## 🆘 Support

If you encounter issues:
1. Check the server logs for detailed error messages
2. Run the test script to identify specific problems
3. Verify all environment variables are set correctly
4. Ensure database is properly configured and running
