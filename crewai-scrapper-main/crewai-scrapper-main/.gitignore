# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build directories
build/
dist/

# Test directories
test-results/
coverage/

# Database files
*.sqlite
*.sqlite3
*.db

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# IDE files
*.sublime-project
*.sublime-workspace

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local configuration files
config/local.json
config/local.js

# PM2 files
ecosystem.config.js
.pm2/

# Docker files (optional - uncomment if you don't want to track them)
# Dockerfile
# docker-compose.yml
# .dockerignore

# Kubernetes files (optional)
# *.yaml
# *.yml

# Terraform files (optional)
# *.tfstate
# *.tfstate.*
# .terraform/

# AWS files
.aws/

# Google Cloud files
.gcloud/

# Azure files
.azure/

# Serverless Framework
.serverless/

# Webpack
.webpack/

# Rollup
.rollup.cache/

# Parcel
.parcel-cache/

# ESLint
.eslintcache

# Prettier
.prettierignore

# Jest
coverage/
.nyc_output/

# Cypress
cypress/videos/
cypress/screenshots/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Local development files
*.local
.local/

# Cache directories
.cache/
.tmp/

# Lock files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Generated documentation
docs/generated/
api-docs/

# Scraped data (specific to your scraping project)
scraped-data/
downloads/
output/
data/
results/

# Browser automation files
.puppeteer/
.playwright/
screenshots/
pdfs/

# API keys and secrets (additional protection)
secrets/
keys/
certificates/
*.pem
*.key
*.crt
*.p12
*.pfx

# Monitoring and analytics
.newrelic/
.datadog/

# Performance monitoring
.clinic/

/src/generated/prisma
