// Test script for direct API with AI enhancement
import axios from 'axios';

const BASE_URL = 'http://localhost:4000/api';

async function testDirectAPIWithAI() {
  try {
    console.log('🧪 Testing Direct Scraping API with AI Enhancement...\n');

    const testQueries = [
      'baggy jeans',
      'running shoes',
      'gaming laptop',
      'winter jacket'
    ];

    for (const query of testQueries) {
      console.log(`\n🔍 Testing direct API with: "${query}"`);
      console.log(`📡 URL: ${BASE_URL}/scraping?query=${encodeURIComponent(query)}&page=1`);
      
      try {
        const response = await axios.get(`${BASE_URL}/scraping`, {
          params: {
            query: query,
            page: 1,
            limit: 3 // Small limit for testing
          }
        });

        const data = response.data.data;
        console.log(`✅ Original Query: "${data.query}"`);
        console.log(`🔧 Refined Query: "${data.refinedQuery}"`);
        console.log(`🤖 AI Enhanced Query: "${data.aiEnhancedQuery}"`);
        console.log(`✨ AI Enhanced: ${data.aiEnhanced ? 'YES' : 'NO'}`);
        console.log(`📦 Products Found: ${data.totalProducts}`);
        
        if (data.products && data.products.length > 0) {
          console.log(`🎯 Sample Product: "${data.products[0].title}"`);
        }
        
      } catch (error) {
        console.log(`❌ Failed: ${error.response?.data?.message || error.message}`);
        if (error.response?.status === 500) {
          console.log(`💡 This might be due to missing OpenAI API key or network issues`);
        }
      }
    }

    console.log('\n🎉 Direct API AI test completed!');
    console.log('\n📝 Summary:');
    console.log('- The direct scraping API now includes AI enhancement');
    console.log('- Original query → Refined query → AI enhanced query');
    console.log('- Products are fetched using the AI-enhanced search term');
    console.log('- Response includes all query transformations for transparency');

  } catch (error) {
    console.error('\n❌ Test setup failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Tip: Make sure the server is running on port 4000');
      console.log('   Run: npm start in the backend directory');
    }
  }
}

// Run the test
testDirectAPIWithAI();
