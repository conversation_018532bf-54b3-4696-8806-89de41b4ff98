import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const AccessTokenEntity = new EntitySchema({
  name: "AccessToken",
  tableName: "access_tokens",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    hashedToken: {
      type: "varchar",
      unique: true,
      nullable: false,
    },
    isExpired: {
      type: "boolean",
      default: false,
      nullable: false,
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
    updatedAt: {
      type: "timestamp",
      updateDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
  relations: {
    user: {
      type: "many-to-one",
      target: "User",
      joinColumn: true,
      onDelete: "CASCADE",
    },
    refreshToken: {
      type: "many-to-one",
      target: "RefreshToken",
      joinColumn: true,
      onDelete: "CASCADE",
    },
  },
});

export const getAccessTokenRepo = () =>
  AppDataSource.getRepository(AccessTokenEntity);
