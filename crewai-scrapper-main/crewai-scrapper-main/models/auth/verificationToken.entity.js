import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const VerificationTokenType = {
  SIGNUP: "SIGNUP",
  RESET_PASSWORD: "RESET_PASSWORD",
};

export const VerificationTokenEntity = new EntitySchema({
  name: "VerificationToken",
  tableName: "verification_tokens",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    hashedToken: {
      type: "varchar",
      nullable: false,
    },
    type: {
      type: "enum",
      enum: Object.values(VerificationTokenType),
      nullable: false,
    },
    userId: {
      type: "uuid",
      nullable: false,
    },
    expires: {
      type: "timestamp",
      nullable: false,
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
    updatedAt: {
      type: "timestamp",
      updateDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
});

export const getVerificationTokenRepo = () =>
  AppDataSource.getRepository(VerificationTokenEntity);
