import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const OtpTypes = {
  SIGNUP_REQUEST: "SIGNUP_REQUEST",
  FORGET_PASSWORD_REQUEST: "FORGET_PASSWORD_REQUEST",
};

const OTP_TYPES = Object.values(OtpTypes);

export const OtpEntity = new EntitySchema({
  name: "Otp",
  tableName: "otps",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    email: {
      type: "varchar",
      nullable: false,
      nullable: false,
    },
    otpHash: {
      type: "varchar",
      nullable: false,
      nullable: false,
    },
    isExpired: {
      type: "boolean",
      default: false,

      nullable: false,
    },
    type: {
      type: "enum",
      enum: OTP_TYPES,

      nullable: false,
    },
    expiresAt: {
      type: "timestamp",
      nullable: false,

      nullable: false,
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
    updatedAt: {
      type: "timestamp",
      updateDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
});

export const getOtpRepo = () => AppDataSource.getRepository(OtpEntity);
