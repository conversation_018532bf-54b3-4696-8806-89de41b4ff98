import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const RefreshTokenEntity = new EntitySchema({
  name: "RefreshToken",
  tableName: "refresh_tokens",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    token: {
      type: "text",
      unique: true,
      nullable: false,
    },
    hashedToken: {
      type: "varchar",
      unique: true,
      nullable: false,
    },
    isExpired: {
      type: "boolean",
      default: false,
      nullable: false,
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
    updatedAt: {
      type: "timestamp",
      updateDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
  relations: {
    user: {
      type: "many-to-one",
      target: "User",
      joinColumn: true,
      onDelete: "CASCADE",
    },
  },
});

export const getRefreshTokenRepo = () =>
  AppDataSource.getRepository(RefreshTokenEntity);
