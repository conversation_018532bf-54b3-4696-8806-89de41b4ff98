import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const OldProductEntity = new EntitySchema({
  name: "OldProduct",
  tableName: "old_products",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    image: {
      type: "varchar",
      length: 500,
      nullable: true,
    },
    title: {
      type: "varchar",
      length: 500,
      nullable: false,
    },
    price: {
      type: "varchar",
      length: 100,
      nullable: true,
    },
    minimumOrderQuantity: {
      type: "varchar",
      length: 100,
      nullable: true,
    },
    platform: {
      type: "varchar",
      length: 100,
      nullable: true,
    },
    registeredSupplier: {
      type: "boolean",
      default: false,
      nullable: false,
    },
    supplierName: {
      type: "varchar",
      length: 200,
      nullable: true,
    },
    registerTime: {
      type: "varchar",
      length: 50,
      nullable: true,
    },
    rating: {
      type: "decimal",
      precision: 3,
      scale: 2,
      nullable: true,
    },
    customerInterestNumber: {
      type: "integer",
      nullable: true,
    },
    gender: {
      type: "enum",
      enum: ["men", "women", "both"],
      nullable: false,
      default: "both",
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
    updatedAt: {
      type: "timestamp",
      updateDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
});

export const getProductRepo = () => AppDataSource.getRepository(ProductEntity);
