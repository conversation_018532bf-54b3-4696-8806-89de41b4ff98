// src/entities/FavoriteEntity.ts
import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const FavouriteEntity = new EntitySchema({
  name: "Favourite",
  tableName: "favourites",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
  relations: {
    user: {
      type: "many-to-one",
      target: "User",
      joinColumn: { name: "userId" },
      nullable: false,
      eager: false,
    },
    product: {
      type: "many-to-one",
      target: "Product",
      joinColumn: { name: "productId" },
      nullable: false,
      eager: true,
    },
  },
});

export const getFavouriteRepo = () =>
  AppDataSource.getRepository(FavouriteEntity);
