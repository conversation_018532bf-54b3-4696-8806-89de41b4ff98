import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const ProductEntity = new EntitySchema({
  name: "Product",
  tableName: "products",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    imageUrl: {
      type: "varchar",
      length: 500,
      nullable: false,
    },
    productUrl: {
      type: "varchar",
      length: 1000,
      nullable: false,
    },
    title: {
      type: "varchar",
      length: 500,
      nullable: false,
    },
    price: {
      type: "varchar",
      nullable: true,
    },
    minimumOrderQuantity: {
      type: "varchar",
      length: 100,
      nullable: true,
    },
    platform: {
      type: "varchar",
      length: 100,
      nullable: true,
    },
    rating: {
      type: "decimal",
      precision: 3,
      scale: 2,
      nullable: true,
    },
    customerInterestNumber: {
      type: "integer",
      nullable: true,
    },
    genderCategory: {
      type: "enum",
      enum: ["men", "women", "both", "kids", "unisex", "all"],
      nullable: false,
      default: "all",
    },
    productCategory: {
      type: "varchar",
      length: 100,
      nullable: false,
    },
    additionalDetails: {
      type: "simple-json",
      nullable: true,
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
    updatedAt: {
      type: "timestamp",
      updateDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
  relations: {
    vendor: {
      type: "many-to-one",
      target: "Vendor",
      joinColumn: { name: "vendorId" },
      eager: false,
      nullable: false,
    },
  },
});

export const getProductRepo = () => AppDataSource.getRepository(ProductEntity);
