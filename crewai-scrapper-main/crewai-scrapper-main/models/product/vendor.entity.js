import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const VendorEntity = new EntitySchema({
  name: "Vendor",
  tableName: "vendors",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    name: {
      type: "varchar",
      length: 255,
      unique: true,
      nullable: false,
    },
    registerOn: {
      type: "timestamp",
      nullable: true,
    },
    isVerified: {
      type: "boolean",
      default: false,
    },
    websiteUrl: {
      name: "website_url",
      type: "varchar",
      length: 500,
      nullable: true,
    },
    numOfProducts: {
      type: "int",
      default: 1,
    },
    country: {
      type: "varchar",
      length: 100,
      nullable: false,
    },
    region: {
      type: "varchar",
      length: 100,
      nullable: true,
    },
    city: {
      type: "varchar",
      length: 100,
      nullable: true,
    },
    additional: {
      type: "simple-json",
      nullable: true,
    },
  },
  relations: {
    products: {
      type: "one-to-many",
      target: "Product",
      inverseSide: "vendor",
    },
  },
});

export const getVendorRepo = () => AppDataSource.getRepository(VendorEntity);
