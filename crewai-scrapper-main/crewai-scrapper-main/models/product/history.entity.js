import { EntitySchema, JoinColumn } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const HistoryEntity = new EntitySchema({
  name: "History",
  tableName: "history",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    term: {
      type: "text",
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
  relations: {
    user: {
      type: "many-to-one",
      target: "User",
      joinColumn: { name: "userId" },
      nullable: false,
      onDelete: "CASCADE",
      eager: true,
    },
  },
});

export const getHistoryRepo = () => AppDataSource.getRepository(HistoryEntity);
