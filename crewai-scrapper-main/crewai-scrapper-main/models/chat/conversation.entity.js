import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const ConversationEntity = new EntitySchema({
  name: "Conversation",
  tableName: "conversations",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    title: {
      type: "varchar",
      length: 255,
      nullable: true,
    },
    initialSearchTerm: {
      type: "text",
      nullable: false,
    },
    lastSearchTerm: {
      type: "text",
      nullable: true,
    },
    context: {
      type: "jsonb",
      nullable: true,
    },
    isActive: {
      type: "boolean",
      default: true,
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
    updatedAt: {
      type: "timestamp",
      updateDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
  relations: {
    user: {
      type: "many-to-one",
      target: "User",
      joinColumn: { name: "userId" },
      nullable: false,
      onDelete: "CASCADE",
      eager: false,
    },
    messages: {
      type: "one-to-many",
      target: "Message",
      inverseSide: "conversation",
    },
  },
});

export const getConversationRepo = () => AppDataSource.getRepository(ConversationEntity);
