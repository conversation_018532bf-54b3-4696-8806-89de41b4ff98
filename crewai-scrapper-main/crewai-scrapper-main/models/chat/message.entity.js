import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const MessageEntity = new EntitySchema({
  name: "Message",
  tableName: "messages",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    content: {
      type: "text",
      nullable: false,
    },
    role: {
      type: "enum",
      enum: ["user", "assistant", "system"],
      nullable: false,
    },
    searchTermGenerated: {
      type: "text",
      nullable: true,
    },
    metadata: {
      type: "jsonb",
      nullable: true,
    },
    createdAt: {
      type: "timestamp",
      createDate: true,
      default: () => "CURRENT_TIMESTAMP",
    },
  },
  relations: {
    conversation: {
      type: "many-to-one",
      target: "Conversation",
      joinColumn: { name: "conversationId" },
      nullable: false,
      onDelete: "CASCADE",
      eager: false,
    },
  },
});

export const getMessageRepo = () => AppDataSource.getRepository(MessageEntity);
