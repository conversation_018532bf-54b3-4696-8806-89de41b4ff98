import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const GlobalFilterEntity = new EntitySchema({
  name: "Global",
  tableName: "global",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    menCategories: {
      type: "jsonb",
      nullable: true,
    },
    womenCategories: {
      type: "jsonb",
      nullable: true,
    },
    otherCategories: {
      type: "jsonb",
      nullable: true,
    },
  },
});

export const getGlobalRepo = () =>
  AppDataSource.getRepository(GlobalFilterEntity);
