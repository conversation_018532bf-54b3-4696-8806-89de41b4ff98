import { EntitySchema } from "typeorm";
import { AppDataSource } from "../../config/index.js";

export const BulkCsvFileEntity = new EntitySchema({
  name: "BulkCsvFile",
  tableName: "bulkcsvfile",
  columns: {
    id: {
      primary: true,
      type: "uuid",
      generated: "uuid",
    },
    url: {
      type: "varchar",
      nullable: false,
    },
    status: {
      type: "enum",
      enum: ["pending", "success", "fail", "cancelled", "incomplete"],
      default: "pending",
    },
    reportUrl: {
      name: "report_url",
      type: "varchar",
      nullable: true,
    },
    createdAt: {
      name: "created_at",
      type: "timestamp",
      createDate: true,
    },
    updatedAt: {
      name: "updated_at",
      type: "timestamp",
      updateDate: true,
    },
  },
});

export const getBulkCsvFileRepo = () =>
  AppDataSource.getRepository(BulkCsvFileEntity);
