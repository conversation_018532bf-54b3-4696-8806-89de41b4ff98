// Simple test script to verify chat endpoints are working
import axios from 'axios';

const BASE_URL = 'http://localhost:4000/api';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

const testMessage = 'I need to find some running shoes for men';

async function testChatEndpoints() {
  try {
    console.log('🧪 Testing Chat Endpoints...\n');

    // 1. Login to get auth token (assuming you have a test user)
    console.log('1. Attempting to login...');
    let authToken = '';
    
    try {
      const loginResponse = await axios.post(`${BASE_URL}/login`, testUser);
      authToken = loginResponse.data.data.accessToken;
      console.log('✅ Login successful');
    } catch (error) {
      console.log('⚠️  Login failed (expected if no test user exists)');
      console.log('   You may need to create a test user first');
      return;
    }

    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };

    // 2. Create a new conversation
    console.log('\n2. Creating new conversation...');
    const conversationResponse = await axios.post(
      `${BASE_URL}/chat/conversations`,
      { initialMessage: testMessage },
      { headers }
    );
    
    console.log('✅ Conversation created successfully');
    console.log('   Conversation ID:', conversationResponse.data.data.conversation.id);
    console.log('   Search Term Generated:', conversationResponse.data.data.searchTerm);
    
    const conversationId = conversationResponse.data.data.conversation.id;

    // 3. Add a follow-up message
    console.log('\n3. Adding follow-up message...');
    const messageResponse = await axios.post(
      `${BASE_URL}/chat/conversations/${conversationId}/messages`,
      { message: 'Actually, I prefer Nike brand shoes under $100' },
      { headers }
    );
    
    console.log('✅ Message added successfully');
    console.log('   New Search Term:', messageResponse.data.data.searchTerm);

    // 4. Get conversation with messages
    console.log('\n4. Retrieving conversation...');
    const getConversationResponse = await axios.get(
      `${BASE_URL}/chat/conversations/${conversationId}`,
      { headers }
    );
    
    console.log('✅ Conversation retrieved successfully');
    console.log('   Total Messages:', getConversationResponse.data.data.messages.length);

    // 5. Get user conversations list
    console.log('\n5. Getting user conversations...');
    const conversationsResponse = await axios.get(
      `${BASE_URL}/chat/conversations`,
      { headers }
    );
    
    console.log('✅ Conversations list retrieved successfully');
    console.log('   Total Conversations:', conversationsResponse.data.data.conversations.length);

    // 6. Test chat-integrated search
    console.log('\n6. Testing chat-integrated search...');
    const searchResponse = await axios.post(
      `${BASE_URL}/scraping/chat-search`,
      { 
        conversationId: conversationId,
        searchTerm: 'nike running shoes men'
      },
      { headers }
    );
    
    console.log('✅ Chat-integrated search successful');
    console.log('   Products Found:', searchResponse.data.data.products.length);

    console.log('\n🎉 All chat endpoints are working correctly!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Tip: Make sure you have a valid user account and the correct credentials');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Tip: Make sure the server is running on port 4000');
    } else if (error.response?.status === 500) {
      console.log('\n💡 Tip: Check server logs for detailed error information');
      console.log('   Common issues:');
      console.log('   - Missing OPENAI_API_KEY environment variable');
      console.log('   - Database connection issues');
      console.log('   - Missing database tables (run migrations)');
    }
  }
}

// Run the test
testChatEndpoints();
