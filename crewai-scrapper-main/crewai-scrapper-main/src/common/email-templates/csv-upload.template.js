export const generateCsvUploadEmailTemplate = ({
  successCount,
  failCount,
  reportUrl,
  message,
}) => {
  return `
      <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: auto; border: 1px solid #ddd; border-radius: 6px;">
        <h2 style="color: #4CAF50;">CSV Upload Summary</h2>
        <p><strong>Message:</strong> ${message}</p>
        <p><strong>Success Count:</strong> ${successCount}</p>
        <p><strong>Fail Count:</strong> ${failCount}</p>
  
        ${
          failCount > 0 && reportUrl
            ? `<p><strong>Report Link:</strong> <a href="${reportUrl}" target="_blank">${reportUrl}</a></p>`
            : ""
        }
  
        <p style="font-size: 13px; color: #999;">This is an automated message.</p>
      </div>
    `;
};
