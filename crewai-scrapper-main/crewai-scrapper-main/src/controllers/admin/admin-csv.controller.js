import { StatusCodes } from "http-status-codes";
import { CustomApiError } from "../../utils/errors/index.js";
import {
  getProductRepo,
  getVendorRepo,
} from "../../../models/product/index.js";
import { getBulkCsvFileRepo } from "../../../models/product/index.js";
import cloudinary from "../../../config/cloudinary.config.js";
import fs from "fs";
import { Readable } from "stream";
import csv from "csv-parser";
import { createObjectCsvWriter } from "csv-writer";
import { askOpenAI } from "../../third-party/OpenAI/index.js";
import { In } from "typeorm";
import { generateCsvUploadEmailTemplate } from "../../common/email-templates/csv-upload.template.js";
import { SendEmail } from "../../third-party/Resend/resend.service.js";

// ----------------------------- Bulk Products -----------------------------

export const uploadBulkProductsUsingCSV = async (req, res) => {
  // validate file
  const file = req.file;
  if (!file) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "CSV file is required");
  }

  const csvBuffer = Buffer.from(file.buffer);
  const bulkRepo = getBulkCsvFileRepo();
  let savedFile;

  try {
    // upload file to cloudinary
    const cloudinaryCsvInfo = await uploadBufferToCloudinary(
      csvBuffer,
      file.originalname,
      "csv-files"
    );

    // parse csv buffer
    const rows = await parseCsvBuffer(csvBuffer);

    // save file url to database with status pending
    savedFile = await bulkRepo.save({
      url: cloudinaryCsvInfo.secure_url,
      status: "pending",
    });

    // initialize product repo
    const productRepo = getProductRepo();

    // initialize success and failed arrays
    const success = [];
    const failed = [];

    // format rows with AI
    const formattedRows = [];
    for (const row of rows) {
      try {
        const formatted = await formatRowWithAI(row);
        formattedRows.push(formatted);
      } catch (err) {
        failed.push({ ...row, error: err.message });
      }
    }

    // check for duplicates
    const titles = formattedRows.map((p) => p.title);
    const existingProducts = await productRepo.find({
      where: { title: In(titles) },
    });

    // check if product already exists
    const isDuplicate = (product) => {
      return existingProducts.some(
        (existing) =>
          existing.title === product.title &&
          existing.price === product.price &&
          existing.platform === product.platform
      );
    };

    // create products
    for (const formatted of formattedRows) {
      try {
        // check if vendor only
        const isVendorOnly =
          !formatted.title && !formatted.price && formatted.vendorName;

        // if vendor only, find or create vendor
        if (isVendorOnly) {
          try {
            await findOrCreateVendor(formatted);
          } catch (err) {
            failed.push({ ...formatted, error: err.message });
          }
          continue;
        }

        // check if product already exists
        if (isDuplicate(formatted)) {
          continue;
        }

        // find or create vendor
        const vendor = await findOrCreateVendor(formatted);

        // create product
        const newProduct = productRepo.create({
          imageUrl: formatted.imageUrl,
          productUrl: formatted.productUrl,
          title: formatted.title,
          price: formatted.price ? parseFloat(formatted.price) : null,
          minimumOrderQuantity: formatted.minimumOrderQuantity
            ? parseInt(formatted.minimumOrderQuantity)
            : null,
          platform: formatted.platform,
          rating: formatted.rating ? parseFloat(formatted.rating) : null,
          customerInterestNumber: formatted.customerInterestNumber
            ? parseInt(formatted.customerInterestNumber)
            : null,
          genderCategory: formatted.genderCategory,
          productCategory: formatted.productCategory,
          additionalDetails: formatted.additionalDetails || {},
          vendor,
        });

        // save product to database
        await productRepo.save(newProduct);

        // add product to success array
        success.push(newProduct);
      } catch (err) {
        failed.push({ ...formatted, error: err.message });
      }
    }

    // generate report url
    let reportUrl = null;

    // if there are failed products, generate report url
    if (failed.length > 0) {
      const failPath = `/tmp/fail-${Date.now()}.csv`;

      // generate fail csv report
      await generateFailCsvReport(failed, failPath);

      // upload fail csv report to cloudinary
      const upload = await uploadBufferToCloudinary(
        fs.readFileSync(failPath),
        "fail-report.csv",
        "Reports"
      );
      reportUrl = upload.secure_url;
      // delete fail csv report
      fs.unlinkSync(failPath);
    }

    // update bulk csv file status
    await bulkRepo.update(savedFile.id, {
      status: failed.length > 0 ? "fail" : "success",
      reportUrl,
    });

    // === Send Admin Email (Preview only for now) ===
    const html = generateCsvUploadEmailTemplate({
      successCount: success.length,
      failCount: failed.length,
      reportUrl,
      message:
        failed.length > 0
          ? "Product upload completed with some failed rows."
          : "Product upload completed successfully.",
    });

    await SendEmail(
      process.env.APP_ADMIN_MAIL,
      "CSV Upload Report: Products",
      html
    );

    return;
  } catch (error) {
    // if file was saved, update status to incomplete
    if (savedFile?.id) {
      await bulkRepo.update(savedFile.id, {
        status: "incomplete",
      });
    }

    return;
  }
};

// ----------------------------- Vendors -----------------------------
export const uploadBulkVendorsUsingCSV = async (req, res) => {
  // validate file
  const file = req.file;
  if (!file) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "CSV file is required");
  }

  // convert file buffer to csv buffer
  const csvBuffer = Buffer.from(file.buffer);

  // initialize bulk csv file repo
  const bulkRepo = getBulkCsvFileRepo();

  // initialize saved file
  let savedFile;

  try {
    // upload file to cloudinary
    const cloudinaryCsvInfo = await uploadBufferToCloudinary(
      csvBuffer,
      file.originalname,
      "csv-files"
    );

    // parse csv buffer
    const rows = await parseCsvBuffer(csvBuffer);

    // save file url to database with status pending
    savedFile = await bulkRepo.save({
      url: cloudinaryCsvInfo.secure_url,
      status: "pending",
    });

    // initialize vendor repo
    const vendorRepo = getVendorRepo();

    // initialize success and failed arrays
    const success = [];
    const failed = [];

    // format rows with AI
    const formattedRows = [];
    for (const row of rows) {
      try {
        const formatted = await formatRowWithAI(row);
        formattedRows.push(formatted);
      } catch (err) {
        failed.push({ ...row, error: err.message });
      }
    }

    for (const formatted of formattedRows) {
      try {
        const normalized = {
          name: formatted.vendorName,
          registerOn: formatted.registerTime,
          country: formatted.country,
          region: formatted.region || null,
          city: formatted.city || null,
          websiteUrl:
            formatted.websiteUrl || formatted.additionalDetails?.websiteUrl,
          isVerified: formatted.additionalDetails?.isVerified === "True",
          numOfProducts: parseInt(
            formatted.additionalDetails?.numOfProducts || "0",
            10
          ),
          additional: formatted.additionalDetails?.additional || null,
        };

        const existingVendor = await vendorRepo.findOne({
          where: [
            { name: normalized.name },
            { websiteUrl: normalized.websiteUrl },
            { name: normalized.name, country: normalized.country },
          ],
        });

        if (existingVendor) {
          failed.push({ ...normalized, error: "Vendor already exists" });
          continue;
        }

        const newVendor = vendorRepo.create(normalized);
        await vendorRepo.save(newVendor);
        success.push(newVendor);
      } catch (err) {
        failed.push({ ...formatted, error: err.message });
      }
    }

    // generate report url
    let reportUrl = null;

    // if there are failed vendors, generate report url
    if (failed.length > 0) {
      const failPath = `/tmp/fail-${Date.now()}.csv`;
      await generateFailCsvReport(failed, failPath);
      const upload = await uploadBufferToCloudinary(
        fs.readFileSync(failPath),
        "fail-report.csv",
        "Reports"
      );
      reportUrl = upload.secure_url;
      fs.unlinkSync(failPath);
    }

    // update bulk csv file status
    await bulkRepo.update(savedFile.id, {
      status: failed.length > 0 ? "fail" : "success",
      reportUrl,
    });

    // === Send Admin Email (Preview only for now) ===
    const html = generateCsvUploadEmailTemplate({
      successCount: success.length,
      failCount: failed.length,
      reportUrl,
      message:
        failed.length > 0
          ? "Vendor upload completed with some failed rows."
          : "Vendor upload completed successfully.",
    });

    await SendEmail(
      process.env.APP_ADMIN_MAIL,
      "CSV Upload Report: Vendors",
      html
    );

    return;
  } catch (error) {
    // if file was saved, update status to incomplete
    if (savedFile?.id) {
      await bulkRepo.update(savedFile.id, {
        status: "incomplete",
      });
    }

    return;
  }
};

// ----------------------------- Helper Functions -----------------------------

export const formatRowWithAI = async (row) => {
  const prompt = `
You are an intelligent CSV row interpreter. You will receive a single row from a CSV file as a JavaScript object. The column names may be unclear, misspelled, or non-standard.

Your task is to transform this row into the following **strict JSON schema**, based on a product and vendor data model:

{
  "title": string,
  "price": string | null,
  "minimumOrderQuantity": string | null,
  "platform": string | null,
  "imageUrl": string,
  "productUrl": string,
  "vendorName": string,
  "rating": number | null,
  "registeredVendor": boolean | null,
  "registerTime": string | null,
  "customerInterestNumber": number | null,
  "genderCategory": "men" | "women" | "both" | "kids" | "unisex" | "all",
  "productCategory": string,
  "country": string,
  "region": string,
  "city": string,
  "additionalDetails": object
}

### Field Requirements:
- The following fields **must NOT be null or empty**:
  - \`title\`, \`imageUrl\`, \`productUrl\`, \`vendorName\`, \`genderCategory\`, \`productCategory\`, \`country\`, \`region\`, \`city\`
- Other fields may be \`null\` if missing or unparseable.
- \`genderCategory\` must be one of: "men", "women", "both", "kids", "unisex", or "all".

### Mapping Instructions:
- Guess field meanings even if column names are incorrect, ambiguous, or in another language (e.g., "productNaam" → "title").
- If a field contains a URL:
  - If it ends with \`.jpg\`, \`.jpeg\`, \`.png\`, \`.webp\`, etc., treat it as **imageUrl**.
  - If it resembles a product listing link or a marketplace page, treat it as **productUrl**.
  - If it could represent both and neither field is set, assign it to **both**.
- Store any unmatched or extra fields in **additionalDetails**.
- Use \`null\` only where allowed (optional fields). Do not use \`null\` for required fields.

### Output Format:
- Return a single, **raw JSON object only**.
- Do **not** return markdown, comments, or any surrounding text.

### Input row:
${JSON.stringify(row)}
`;
  const response = await askOpenAI(prompt);

  try {
    const formatted = JSON.parse(response);
    return formatted;
  } catch (err) {
    throw new Error("Failed to parse AI response: " + err.message);
  }
};

export const generateFailCsvReport = async (failedRows, filePath) => {
  const headers = Object.keys(failedRows[0] || {}).map((key) => ({
    id: key,
    title: key,
  }));

  const csvWriter = createObjectCsvWriter({
    path: filePath,
    header: headers,
  });

  await csvWriter.writeRecords(failedRows);
};

export const findOrCreateVendor = async (formatted) => {
  const {
    vendorName,
    country,
    region,
    city,
    registerTime,
    additionalDetails = {},
    websiteUrl: directWebsiteUrl,
    isVerified: directIsVerified,
  } = formatted;

  const websiteUrl = directWebsiteUrl || additionalDetails.websiteUrl || null;
  const isVerified =
    directIsVerified ||
    additionalDetails.isVerified === "True" ||
    additionalDetails.isVerified === true;

  if (!vendorName || !country || !websiteUrl) {
    throw new Error(
      "Vendor is missing required fields (name, country, or websiteUrl)"
    );
  }

  const vendorRepo = getVendorRepo();

  let vendor = await vendorRepo.findOne({ where: { name: vendorName.trim() } });

  if (vendor) {
    vendor.numOfProducts += 1;
    return await vendorRepo.save(vendor);
  }

  vendor = vendorRepo.create({
    name: vendorName.trim(),
    websiteUrl,
    isVerified,
    registerOn: registerTime ? new Date(registerTime) : new Date(),
    numOfProducts: 1,
    country,
    region: region || null,
    city: city || null,
    additional: additionalDetails,
  });

  return await vendorRepo.save(vendor);
};

export const parseCsvBuffer = (buffer) => {
  const rows = [];
  return new Promise((resolve, reject) => {
    Readable.from(buffer)
      .pipe(csv())
      .on("data", (row) => rows.push(row))
      .on("end", () => resolve(rows))
      .on("error", reject);
  });
};

export const uploadBufferToCloudinary = (
  buffer,
  filename,
  folderPath = "csv-files"
) => {
  return new Promise((resolve, reject) => {
    const stream = cloudinary.uploader.upload_stream(
      {
        resource_type: "raw",
        folder: folderPath,
        public_id: `csv/${Date.now()}_${filename}`,
        format: "csv",
      },
      (error, result) => {
        if (error) reject(error);
        else resolve(result);
      }
    );
    Readable.from(buffer).pipe(stream);
  });
};
