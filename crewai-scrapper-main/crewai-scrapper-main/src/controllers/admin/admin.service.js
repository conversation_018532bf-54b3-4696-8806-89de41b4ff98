import { StatusCodes } from "http-status-codes";
import { CustomApiError } from "../../utils/errors/index.js";
import {
  getProductRepo,
  getVendorRepo,
} from "../../../models/product/index.js";
import { createProductValidation } from "./validations/product.validations.js";
import { getGlobalRepo } from "../../../models/filter/globalFilter.entity.js";
import { CustomLogger } from "../../utils/logger/logger.js";
import { createVendorValidation } from "./validations/vendor.validations.js";

// ----------------------------- Products -----------------------------

// Validation function for product creation
export const validateProductCreation = async (productData) => {
  const { error } = createProductValidation.validate(productData);

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.context.key,
      message: detail.message,
    }));

    throw new CustomApiError(StatusCodes.BAD_REQUEST, errors[0]?.message);
  }
};

// Validation function for vendor creation
export const validateVendorCreation = async (vendorData) => {
  const { error } = createVendorValidation.validate(vendorData);

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.context.key,
      message: detail.message,
    }));

    throw new CustomApiError(StatusCodes.BAD_REQUEST, errors[0]?.message);
  }
};

// Create a new vendor manually
export const createSingleVendor = async (vendorData) => {
  const vendorRepo = getVendorRepo();

  await validateVendorCreation(vendorData);

  const { name, websiteUrl, country } = vendorData;

  // Simple duplicate check using all three fields
  const existingVendor = await vendorRepo.findOne({
    where: {
      name,
      websiteUrl,
      country,
    },
  });

  if (existingVendor) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Vendor already exists with the same name, website URL, and country"
    );
  }
  const newVendor = vendorRepo.create(vendorData);

  // Save to database
  const savedVendor = await vendorRepo.save(newVendor);

  return savedVendor;
};

// Create a new product manually
export const createSingleProduct = async (productData, vendor) => {
  const productRepo = getProductRepo();

  try {
    // Map frontend fields to database schema and include vendor
    const mappedProductData = {
      imageUrl: productData.image,
      productUrl: productData.productUrl, // Default empty since not provided from frontend
      title: productData.title,
      price: productData.price, // Already a string from frontend
      minimumOrderQuantity: productData.minimumOrderQuantity
        ? productData.minimumOrderQuantity.toString()
        : null,
      platform: productData.platform,
      rating: productData.rating,
      customerInterestNumber: productData.customerInterestNumber,
      genderCategory:
        productData.gender === "all" ? "both" : productData.gender,
      productCategory: productData.category,
      additionalDetails: {
        registerTime: productData.registerTime,
      },
      vendor: vendor, // Set the vendor relation
    };

    const newProduct = productRepo.create(mappedProductData);

    // Save to database
    const savedProduct = await productRepo.save(newProduct);

    // Return product with vendor information
    return await productRepo.findOne({
      where: { id: savedProduct.id },
      relations: ["vendor"],
    });
  } catch (error) {
    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      "Failed to create product: " + error.message
    );
  }
};

// ----------------------------- Global Filters -----------------------------

export const getGlobalFiltersService = async () => {
  const globalRepo = getGlobalRepo();

  try {
    const globalFilter = await globalRepo.find();

    if (!globalFilter || globalFilter.length === 0) {
      return {
        menCategories: null,
        womenCategories: null,
        otherCategories: null,
      };
    }

    return {
      menCategories: globalFilter[0].menCategories,
      womenCategories: globalFilter[0].womenCategories,
      otherCategories: globalFilter[0].otherCategories,
    };
  } catch (error) {
    CustomLogger.error(error);

    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      "Failed to get global gender filter"
    );
  }
};

export const getGlobalCategoriesService = async () => {
  const globalRepo = getGlobalRepo();
  const globalFilter = await globalRepo.find();
  return globalFilter;
};

export const updateGlobalFiltersService = async (filters) => {
  const globalRepo = getGlobalRepo();
  try {
    let globalFilter = await globalRepo.find(); // Since primary key is the single column

    if (!globalFilter[0]) {
      globalFilter[0] = globalRepo.create({ filters });
    } else {
      globalFilter[0].menCategories = filters.menCategories;
      globalFilter[0].womenCategories = filters.womenCategories;
      globalFilter[0].otherCategories = filters.otherCategories;
    }
    await globalRepo.save(globalFilter);
    return globalFilter;
  } catch (error) {
    CustomLogger.error(error);

    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      "Failed to update global filters"
    );
  }
};

export const updateGlobalCategoriesService = async (categories) => {
  const globalRepo = getGlobalRepo();

  try {
    let globalFilter = await globalRepo.find();

    if (!globalFilter[0]) {
      const newRecord = globalRepo.create({
        menCategories: categories.menCategory,
        womenCategories: categories.womenCategory,
        otherCategories: categories.otherCategory,
      });

      const savedRecord = await globalRepo.save(newRecord);
      return savedRecord;
    } else {
      globalFilter[0].menCategories = categories.menCategory;
      globalFilter[0].womenCategories = categories.womenCategory;
      globalFilter[0].otherCategories = categories.otherCategory;

      const updated = await globalRepo.save(globalFilter[0]);
      return updated;
    }
  } catch (error) {
    CustomLogger.error(error);
    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      "Failed to update global categories"
    );
  }
};
