import { getGlobalCategoriesService } from "./admin.service.js";
import { askOpenAI } from "../../third-party/OpenAI/index.js";
export const analyzeAndRefineSearchTerm = async (searchTerm) => {
  const prompt = `Analyze the following search term and classify it into the most appropriate product category. 

  Categories (choose one):
  - clothing
  - footwear  
  - jewelry
  - bags
  - electronics
  - beauty
  - hair_care
  - skincare
  - grooming
  - home_goods
  - sports
  - books
  - other

  Rules:
  - Return only the category name in lowercase
  - If the term doesn't clearly fit any category, return "other"
  - If multiple categories apply, choose the most specific one

  Search term: '${searchTerm}'`;

  const category = await askOpenAI(prompt);
  const normalizedCategory = category.trim().toLowerCase();

  const globalFilter = await getGlobalCategoriesService();
  const menCategories = globalFilter?.[0]?.menCategories || [];
  const womenCategories = globalFilter?.[0]?.womenCategories || [];

  const normalizedMen = menCategories.map((c) => c.toLowerCase());
  const normalizedWomen = womenCategories.map((c) => c.toLowerCase());

  const isMenCategory = normalizedMen.includes(normalizedCategory);
  const isWomenCategory = normalizedWomen.includes(normalizedCategory);

  let refinedKeyword = searchTerm;
  if (isMenCategory && isWomenCategory) {
    refinedKeyword = `men and women ${searchTerm}`;
  } else if (isMenCategory) {
    refinedKeyword = `men ${searchTerm}`;
  } else if (isWomenCategory) {
    refinedKeyword = `women ${searchTerm}`;
  }

  return { refinedKeyword, category: normalizedCategory };
};

/**
 * Controller for external use
 */
export const analyzeSearchTermAndRefine = async (req, res) => {
  try {
    const { searchTerm } = req.body;
    const result = await analyzeAndRefineSearchTerm(searchTerm);

    res.status(200).json({
      success: true,
      ...result,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to analyze search term",
      error: error.message,
    });
  }
};
