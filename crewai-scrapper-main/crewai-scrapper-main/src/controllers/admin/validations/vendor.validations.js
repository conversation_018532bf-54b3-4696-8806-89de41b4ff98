import Joi from "joi";

export const createVendorValidation = Joi.object({
  name: Joi.string().trim().max(255).required().messages({
    "string.empty": "Vendor name is required",
    "string.max": "Vendor name should have a maximum length of 255 characters",
  }),
  registerOn: Joi.date().optional().messages({
    "date.base": "Register date must be a valid date",
  }),
  isVerified: Joi.boolean().optional().default(false).messages({
    "boolean.base": "isVerified must be a boolean value",
  }),
  websiteUrl: Joi.string().uri().max(500).required().allow(null, "").messages({
    "string.uri": "Website URL must be a valid URL",
    "string.max": "Website URL should have a maximum length of 500 characters",
  }),
  numOfProducts: Joi.number().integer().min(0).optional().default(1).messages({
    "number.base": "Number of products must be a number",
    "number.integer": "Number of products must be an integer",
    "number.min": "Number of products cannot be negative",
  }),
  country: Joi.string().max(100).required().messages({
    "string.base": "Country must be a string",
    "string.empty": "Country is required",
    "string.max": "Country must be at most 100 characters",
  }),
  city: Joi.string().max(100).optional().messages({
    "string.max": "City must be at most 100 characters",
  }),
  region: Joi.string().max(100).optional().messages({
    "string.max": "Region must be at most 100 characters",
  }),
});
