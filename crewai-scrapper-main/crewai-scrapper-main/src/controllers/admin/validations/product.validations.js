import Joi from "joi";

export const createProductValidation = Joi.object({
  image: Joi.string().max(500).optional().messages({
    "string.max": "Image URL should have a maximum length of 500 characters",
  }),
  title: Joi.string().trim().max(500).required().messages({
    "string.empty": "Product title is required",
    "string.max":
      "Product title should have a maximum length of 500 characters",
  }),
  productUrl: Joi.string().trim().uri().required().messages({
    "string.empty": "Product URL is required",
    "string.uri": "Please enter a valid product URL",
  }),
  price: Joi.string().required().messages({
    "string.empty": "Price is required",
  }),
  minimumOrderQuantity: Joi.string().max(100).optional().messages({
    "string.max":
      "Minimum order quantity should have a maximum length of 100 characters",
  }),
  platform: Joi.string().max(100).optional().messages({
    "string.max": "Platform should have a maximum length of 100 characters",
  }),
  registeredVendor: Joi.boolean().optional().default(false).messages({
    "boolean.base": "Registered vendor must be a boolean value",
  }),
  vendorName: Joi.string().max(200).optional().messages({
    "string.max": "Vendor name should have a maximum length of 200 characters",
  }),
  registerTime: Joi.string().max(50).optional().messages({
    "string.max": "Register time should have a maximum length of 50 characters",
  }),
  rating: Joi.number().precision(2).min(0).max(999.99).optional().messages({
    "number.base": "Rating must be a number",
    "number.min": "Rating must be at least 0",
    "number.max": "Rating must not exceed 999.99",
  }),
  customerInterestNumber: Joi.number().integer().optional().messages({
    "number.base": "Customer interest number must be a number",
    "number.integer": "Customer interest number must be an integer",
  }),
  gender: Joi.string()
    .valid("men", "women", "kids", "unisex", "all")
    .required()
    .default("all")
    .messages({
      "any.only":
        "Gender must be one of: men, women, kids, unisex, all (default)",
      "string.empty": "Gender is required",
    }),
  category: Joi.string().trim().max(100).required().messages({
    "string.empty": "Category is required",
    "string.max": "Category should have a maximum length of 100 characters",
  }),
});
