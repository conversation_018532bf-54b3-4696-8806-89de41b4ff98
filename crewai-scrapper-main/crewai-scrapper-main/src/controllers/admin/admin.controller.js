import { StatusCodes } from "http-status-codes";
import { BaseAPIResponse } from "../../utils/responses/index.js";
import {
  validateProductCreation,
  validateVendorCreation,
  createSingleVendor,
  createSingleProduct,
  getGlobalFiltersService,
  updateGlobalFiltersService,
  updateGlobalCategoriesService,
} from "./admin.service.js";
import { setGenderFilterValidation } from "./validations/globalFilter.validations.js";
import { CustomApiError } from "../../utils/errors/CustomApiError.js";
import {
  getProductRepo,
  getVendorRepo,
} from "../../../models/product/index.js";
import { ILike } from "typeorm";
import {
  uploadBulkProductsUsingCSV,
  uploadBulkVendorsUsingCSV,
} from "./admin-csv.controller.js";
import { getGlobalRepo } from "../../../models/filter/globalFilter.entity.js";
import { CustomLogger } from "../../utils/logger/index.js";
import { filterDataList } from "../../constants/filterDataList.js";
// ----------------------------- Products -----------------------------

export const getAllProducts = async (req, res) => {
  const { page = 1, limit = 10, searchTerm = "" } = req.query;

  try {
    const newProRepo = getProductRepo();

    const [result, totalCount] = await newProRepo.findAndCount({
      skip: (page - 1) * limit,
      take: limit,
      where: {
        title: ILike(`%${searchTerm}%`),
      },
      relations: ["vendor"],
      order: {
        createdAt: "ASC",
      },
    });

    const response = new BaseAPIResponse({
      statusCode: StatusCodes.OK,
      data: {
        data: result,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          limit: limit,
          hasNextPage: page < Math.ceil(totalCount / limit),
          hasPrevPage: page > 1,
        },
      },
      message: "Products list retrieved successfully",
    });

    return res.status(StatusCodes.OK).json(response);
  } catch (error) {
    throw error; // Let error middleware handle it
  }
};

export const addSingleProduct = async (req, res) => {
  const { productData, vendorData, vendorId } = req.body;

  try {
    // 1. Validate product data
    await validateProductCreation(productData);

    let vendor;
    const vendorRepo = getVendorRepo();

    // 2. Handle vendor logic (existing or create new)
    if (vendorId) {
      vendor = await vendorRepo.findOne({ where: { id: vendorId } });

      if (!vendor) {
        throw new CustomApiError(
          StatusCodes.NOT_FOUND,
          "Selected vendor not found"
        );
      }

      vendor.numOfProducts += 1;
      await vendorRepo.save(vendor);
    } else if (vendorData) {
      await validateVendorCreation(vendorData);
      vendor = await createSingleVendor(vendorData);
    } else {
      throw new CustomApiError(
        StatusCodes.BAD_REQUEST,
        "Either vendorId or vendorData must be provided"
      );
    }

    // 3. Check for duplicates
    const productRepo = getProductRepo();

    const existingProducts = await productRepo.find({
      where: {
        vendor: { id: vendor.id },
      },
    });

    const isDuplicate = (product) => {
      return existingProducts.some(
        (existing) =>
          existing.title.trim().toLowerCase() ===
            product.title.trim().toLowerCase() &&
          String(existing.price).trim() === String(product.price).trim() &&
          existing.platform.trim().toLowerCase() ===
            product.platform.trim().toLowerCase()
      );
    };

    if (isDuplicate(productData)) {
      throw new CustomApiError(
        StatusCodes.CONFLICT,
        "Duplicate product with same title, price, and platform already exists!"
      );
    }
    // 4. Create product
    const newProduct = await createSingleProduct(productData, vendor);

    // 5. Respond
    const response = new BaseAPIResponse({
      statusCode: StatusCodes.CREATED,
      data: newProduct,
      message: vendorId
        ? "Product added successfully with existing vendor"
        : "Product and vendor created successfully",
    });

    return res.status(StatusCodes.CREATED).json(response);
  } catch (error) {
    throw error;
  }
};

export const getVendorsList = async (req, res) => {
  try {
    const vendorRepo = getVendorRepo();
    const vendors = await vendorRepo.find();

    const response = new BaseAPIResponse({
      statusCode: StatusCodes.OK,
      data: { vendors, message: "Vendors fetched successfully" },
    });
    res.status(response.statusCode).json(response);
  } catch (err) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: "error",
      message: err.message,
    });
  }
};

export const addSingleVendor = async (req, res) => {
  const { vendorData } = req.body;
  await validateVendorCreation(vendorData);

  try {
    const newVendor = await createSingleVendor(vendorData);

    const response = new BaseAPIResponse({
      statusCode: StatusCodes.CREATED,
      data: newVendor,
      message: "Vendor created successfully",
    });

    return res.status(StatusCodes.CREATED).json(response);
  } catch (error) {
    throw error;
  }
};

// ----------------------------- Bulk Uploads -----------------------------
// --Products--
export const addBulkProducts = async (req, res) => {
  res.status(StatusCodes.OK).json({
    message:
      "CSV upload initiated! You will be notified by email once all products are processed.",
  });

  uploadBulkProductsUsingCSV(req, res);
};

// --Vendors--
export const addBulkVendors = async (req, res) => {
  res.status(StatusCodes.OK).json({
    message:
      "CSV upload initiated! You will be notified by email once all vendors are processed.",
  });

  uploadBulkVendorsUsingCSV(req, res);
};

// ----------------------------- Global Filters -----------------------------

export const getAllGlobalFilters = async (req, res) => {
  try {
    const currentFilter = await getGlobalFiltersService();

    const response = new BaseAPIResponse({
      statusCode: StatusCodes.OK,
      data: currentFilter,
      message: "Global filters retrieved successfully",
    });

    return res.status(StatusCodes.OK).json(response);
  } catch (error) {
    throw error; // Let error middleware handle it
  }
};

export const getGlobalCategories = async (req, res) => {
  try {
    const globalData = await getGlobalRepo().findOne({
      where: {}, // Adjust query if needed
      attributes: ["menCategories", "womenCategories"], // Select only these fields
    });

    if (!globalData) {
      return res.status(404).json({
        success: false,
        message: "Global data not found",
      });
    }

    res.status(200).json({
      success: true,
      data: {
        menCategory: globalData.menCategories,
        womenCategory: globalData.womenCategories,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to fetch global categories",
      error: error.message,
    });
  }
};

export const updateGlobalFilters = async (req, res) => {
  const { filters } = req.body;
  // Validate input
  const { error } = setGenderFilterValidation.validate({ filters });

  if (error) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, error.details[0].message);
  }

  try {
    const updatedFilter = await updateGlobalFiltersService(filters);

    return res.status(StatusCodes.OK).json({
      statusCode: StatusCodes.OK,
      message: "Global filters updated successfully",
      data: updatedFilter,
    });
  } catch (error) {
    throw error; // Error handling middleware captures this
  }
};

export const updateGlobalCategories = async (req, res) => {
  const categories = req.body;

  const { error } = setGenderFilterValidation.validate(categories);

  if (error) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      statusCode: StatusCodes.BAD_REQUEST,
      message: error.details[0].message,
    });
  }

  const men = categories.menCategory || [];
  const women = categories.womenCategory || [];

  try {
    const updatedFilter = await updateGlobalCategoriesService(categories);

    return res.status(StatusCodes.OK).json({
      statusCode: StatusCodes.OK,
      message: "Global categories updated successfully",
      data: updatedFilter,
    });
  } catch (error) {
    CustomLogger.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
      message: error.message || "Failed to update global categories",
    });
  }
};
