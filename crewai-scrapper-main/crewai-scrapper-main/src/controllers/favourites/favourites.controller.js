import { StatusCodes } from "http-status-codes";
import { getFavouriteRepo } from "../../../models/product/favourites.entity.js";
import { BaseAPIResponse } from "../../utils/responses/index.js";
import { CustomApiError } from "../../utils/errors/index.js";

export const addToFavourites = async (req, res) => {
  try {
    const userId = req.user.id;
    const { productId } = req.body;
    const favouriteRepo = getFavouriteRepo();

    if (!productId) {
      throw new CustomApiError(
        StatusCodes.BAD_REQUEST,
        "Product ID is required"
      );
    }

    const exsiting = await favouriteRepo.findOne({
      where: {
        user: { id: userId },
        product: { id: productId },
      },
    });

    if (exsiting) {
      throw new CustomApiError(
        StatusCodes.BAD_REQUEST,
        "Product already in favourites"
      );
    }

    const favourite = favouriteRepo.create({
      user: { id: userId },
      product: { id: productId },
    });

    await favouriteRepo.save(favourite);

    const response = new BaseAPIResponse({
      statusCode: StatusCodes.CREATED,
      data: {
        favourite,
        message: "Product successfully added to favourites",
      },
    });
    res.status(response.statusCode).json(response);
  } catch (err) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: "error",
      message: err.message,
    });
  }
};

export const getFavourites = async (req, res) => {
  try {
    const userId = req.user.id;
    const favouriteRepo = getFavouriteRepo();
    const favourites = await favouriteRepo.find({
      where: {
        user: { id: userId },
      },
      relations: ["product", "product.vendor"],
    });
    const products = favourites.map((fav) => fav.product);
    const response = new BaseAPIResponse({
      statusCode: StatusCodes.OK,
      data: {
        favourites: products,
      },
    });
    res.status(response.statusCode).json(response);
  } catch (err) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: "error",
      message: err.message,
    });
  }
};

export const removeFavourite = async (req, res) => {
  try {
    const userId = req.user.id;
    const { productId } = req.params;
    if (!productId) {
      throw new CustomApiError(
        StatusCodes.BAD_REQUEST,
        "Product ID is required"
      );
    }
    const favouriteRepo = getFavouriteRepo();
    const favourite = await favouriteRepo.findOne({
      where: {
        user: { id: userId },
        product: { id: productId },
      },
    });

    if (!favourite) {
      throw new CustomApiError(StatusCodes.NOT_FOUND, "Favourite not found");
    }

    await favouriteRepo.remove(favourite);

    const response = new BaseAPIResponse({
      statusCode: StatusCodes.OK,
      data: {
        message: "Product removed from favourites",
      },
    });
    res.status(response.statusCode).json(response);
  } catch (err) {
    res.status(err.statusCode || StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: "error",
      message: err.message || "Internal Server Error",
    });
  }
};
