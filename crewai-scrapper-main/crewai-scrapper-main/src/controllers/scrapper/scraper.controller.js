import axios from "axios";
import * as cheerio from "cheerio";
import { <PERSON>ike, In } from "typeorm";
import { StatusCodes } from "http-status-codes";
import { CustomApiError } from "../../utils/errors/index.js";
import { AlibabaService } from "./scrapper-services/alibabaService.js";
import { CustomLogger } from "../../utils/logger/logger.js";
import { productsDataList } from "../../bootstrap-data/index.js";
import { BaseAPIResponse } from "../../utils/responses/index.js";
import { determineGender } from "../../bootstrap.js";
import { getProductRepo } from "../../../models/old/old_product.entity.js";

// =====================ALIBABA=====================

// uses puppeteer and crew ai if crew ai's API_KEY is available
// uses puppeteer and cheerio ai if crew ai's API_KEY is NOT available
export const alibabaScrapeProductsList = async (req, res) => {
  const { keyword } = req.query;

  if (!keyword) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "keyword is required");
  }

  const url = `https://www.alibaba.com/trade/search?spm=a2700.galleryofferlist.the-new-header_fy23_pc_search_bar.keydown__Enter&tab=all&SearchText=${keyword}`;

  try {
    const alibabaService = new AlibabaService(); // initialize alibaba class service
    const data = await alibabaService.scrapeProductsList(url); // scrapping called

    res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        data,
        message: "Scrape successful",
      })
    );
  } catch (error) {
    CustomLogger.error("Scrape error:", error);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json(
        new CustomApiError(
          StatusCodes.INTERNAL_SERVER_ERROR,
          "Failed to scrape products list"
        )
      );
  }
};

// uses only cheerio Current Use
export const alibabaScrapeProductListThroughAlibabaApi = async (req, res) => {
  const { query, page = 1, limit = 8 } = req.query;

  const MAX_PER_REQUEST = 4;

  if (!query) {
    return res
      .status(StatusCodes.NOT_FOUND)
      .json(new CustomApiError(StatusCodes.NOT_FOUND, "Query is required"));
  }

  const numericPage = parseInt(page);
  const numericLimit = parseInt(limit);

  // Calculate how many requests are needed
  const requestCount = Math.ceil(numericLimit / MAX_PER_REQUEST);
  const startingPage = (numericPage - 1) * requestCount + 1;

  const urls = Array.from({ length: requestCount }, (_, i) => {
    return `${process.env.ALIBABA_SEARCH_URL}?SearchText=${query}&page=${startingPage + i}`;
  });

  const allProducts = [];

  try {
    for (const url of urls) {
      const { data: html } = await axios.get(url);

      const $ = cheerio.load(html);

      const productCards = $(".fy23-search-card");

      if (productCards.length === 0) {
        CustomLogger.warn(`No products found for URL: ${url}`);
        continue; // don't break; other pages may have results
      }

      productCards.each((_, element) => {
        try {
          const productCard = $(element);

          const title = productCard
            .find(".search-card-e-title span")
            .text()
            .trim();
          const price = productCard
            .find(".search-card-e-price-main")
            .first()
            .text()
            .trim();
          const imageUrl = productCard
            .find(".search-card-e-slider__img")
            .attr("src");
          const productUrl = productCard
            .find(".search-card-e-slider__link")
            .attr("href");
          const minOrder = productCard
            .find(".search-card-m-sale-features__item")
            .first()
            .text()
            .trim();
          const yearsText = productCard
            .find(".search-card-e-supplier__year span")
            .first()
            .text()
            .trim();
          const years = yearsText.split("yrs")[0].trim() + " yrs";

          const isVerified =
            productCard.find(
              'img[src="https://s.alicdn.com/@sc01/kf/H58367af07b91408ab045a753e6b0c41av.png"]'
            ).length > 0;

          const supplier = {
            name: productCard.find(".search-card-e-company").text().trim(),
            years,
            country: productCard
              .find(".search-card-e-country-flag__wrapper img")
              .attr("alt"),
            isVerified,
          };

          const rating = {
            score:
              parseFloat(
                productCard
                  .find(".search-card-e-review span")
                  .first()
                  .text()
                  .trim()
              ) || 0,
            reviews:
              parseInt(
                productCard
                  .find(".search-card-e-review span")
                  .eq(1)
                  .text()
                  .replace(/[()]/g, "")
              ) || 0,
          };

          if (!title || !price || !imageUrl || !productUrl || !supplier.name) {
            CustomLogger.warn("Skipping product with missing data");
            return;
          }

          allProducts.push({
            title,
            price,
            imageUrl,
            productUrl,
            minOrder,
            supplier,
            rating,
          });
        } catch (err) {
          CustomLogger.warn(`Error parsing product: ${err.message}`);
        }
      });
    }

    if (allProducts.length === 0) {
      // If no products are extracted, search for products in the database with the search term
      const productsRepo = getProductRepo();
      const dbProducts = await productsRepo.find({
        where: { title: ILike(`%${query}%`) },
        take: limit,
        skip: (numericPage - 1) * limit,
        order: {
          createdAt: "DESC",
        },
      });

      if (dbProducts.length === 0) {
        return res.status(StatusCodes.OK).json(
          new BaseAPIResponse({
            statusCode: StatusCodes.OK,
            data: {
              products: [],
              total: 0,
              page: numericPage,
              query,
              message: "No valid products found",
            },
            message: "No products extracted or found in the database",
          })
        );
      }

      return res.status(StatusCodes.OK).json(
        new BaseAPIResponse({
          statusCode: StatusCodes.OK,
          data: {
            products: dbProducts,
            total: dbProducts.length,
            page: numericPage,
            query,
            message: "Products found in the database",
          },
          message: "Products retrieved from the database",
        })
      );
    }

    const productsArr = allProducts.map((item) => {
      const gender = determineGender(item.title);

      return {
        image: item.imageUrl ? "https:" + item.imageUrl : null,
        title: item.title,
        price: item.price,
        minimumOrderQuantity: item.minOrder,
        platform: "Alibaba", // Hardcode or dynamically set the platform
        registeredSupplier: item.supplier.isVerified,
        supplierName: item.supplier.name,
        registerTime: new Date().toISOString().split("T")[0], // Use current date or fetch from data
        rating: item.rating.score,
        customerInterestNumber: item.rating.reviews,
        gender,
      };
    });

    const productsRepo = getProductRepo();

    // Fetch all existing titles in bulk
    const existingTitles = (
      await productsRepo.find({
        select: ["title"],
        where: { title: In(productsArr.map((product) => product.title)) },
      })
    ).map((product) => product.title);

    // Filter out products with existing titles
    const productsToSave = productsArr.filter(
      (product) => !existingTitles.includes(product.title)
    );

    const savedProducts = await productsRepo.save(productsToSave);

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        data: {
          products: savedProducts.slice(0, numericLimit),
          total: savedProducts.length,
          page: numericPage,
          query,
          hasMore: savedProducts.length >= numericLimit,
        },
        message: "Products scraped successfully",
      })
    );
  } catch (error) {
    CustomLogger.error("Error scraping products:", error);
    return res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json(
        new CustomApiError(
          StatusCodes.INTERNAL_SERVER_ERROR,
          "Failed to scrape products list"
        )
      );
  }
};

// =====================STATIC DATA=====================

// uses db query
export const scrapeStaticDataFromJSON = async (req, res) => {
  const { query, page = 1, limit = 8 } = req.query;
  const pageNumber = parseInt(page);
  const limitNumber = parseInt(limit);

  // Validate pagination parameters
  if (pageNumber < 1 || isNaN(pageNumber)) {
    return res.status(400).json(new CustomApiError(400, "Invalid page number"));
  }

  if (limitNumber < 1 || isNaN(limitNumber)) {
    return res
      .status(400)
      .json(new CustomApiError(400, "Invalid limit number"));
  }

  if (!query) {
    return res.status(400).json(new CustomApiError(400, "Query is required"));
  }

  const searchQuery = query.toLowerCase();

  const filteredData = productsDataList.filter((item) => {
    const titleMatch = item.title.toLowerCase().includes(searchQuery);
    const supplierMatch = item.supplierName.toLowerCase().includes(searchQuery);
    const platformMatch = item.platform.toLowerCase().includes(searchQuery);

    // Check if price range matches (if query is a number)
    const numericQuery = parseFloat(searchQuery);
    let priceMatch = false;
    if (!isNaN(numericQuery)) {
      const priceRange = item.price.replace("$", "").split(" - ");
      const minPrice = parseFloat(priceRange[0]);
      const maxPrice = parseFloat(priceRange[1]);
      priceMatch = numericQuery >= minPrice && numericQuery <= maxPrice;
    }

    return titleMatch || supplierMatch || platformMatch || priceMatch;
  });

  if (filteredData.length === 0) {
    return res
      .status(404)
      .json(new CustomApiError(404, "No matching products found"));
  }

  // Calculate pagination values
  const startIndex = (pageNumber - 1) * limitNumber;
  const endIndex = startIndex + limitNumber;
  const totalPages = Math.ceil(filteredData.length / limitNumber);
  const paginatedData = filteredData.slice(startIndex, endIndex);

  return res.json(
    new BaseAPIResponse({
      statusCode: 200,
      data: {
        products: paginatedData,
        pagination: {
          currentPage: pageNumber,
          totalPages,
          totalProducts: filteredData.length,
          hasNextPage: pageNumber < totalPages,
          hasPrevPage: pageNumber > 1,
          limit: limitNumber,
        },
      },
      message: "Products found successfully",
    })
  );
};

// =====================THOMASNET=====================

// uses ApiFy (ApiFy API_KEY is required to run this)
export const scrapeSuppliersFromThomasNet = async (req, res) => {
  const ACTOR_ID = "jupri~thomasnet";

  try {
    const { searchTerm, limit = 10 } = req.query;
    const searchingUrl = `https://www.thomasnet.com/suppliers/discover/usa/serving?searchterm=${searchTerm}&search_type=search-suppliers&search_ref=${searchTerm}`;

    if (!searchTerm) {
      return res
        .status(400)
        .json(new CustomApiError(400, "Missing searchTerm in request body"));
    }

    if (limit >= 20) {
      return res
        .status(400)
        .json(
          new CustomApiError(400, "Limit must be less than 20 or equal to 20")
        );
    }

    // Step 1: Start the actor
    const runResponse = await axios.post(
      `https://api.apify.com/v2/acts/${ACTOR_ID}/runs?token=${process.env.APIFY_API_TOKEN}`,
      {
        // INPUT values expected by your actor
        dev_dataset_clear: false,
        dev_no_strip: false,
        limit: limit,
        query: searchingUrl,
      }
    );

    const runId = runResponse.data.data.id;

    // Step 2: Wait for the actor run to finish
    let status = "RUNNING";
    let runResult;

    while (status === "RUNNING" || status === "READY") {
      await new Promise((r) => setTimeout(r, 5000));

      const statusRes = await axios.get(
        `https://api.apify.com/v2/actor-runs/${runId}?token=${process.env.APIFY_API_TOKEN}`
      );

      status = statusRes.data.data.status;
      runResult = statusRes.data.data;
    }

    // Step 3: Get results from dataset
    const datasetId = runResult.defaultDatasetId;

    const datasetRes = await axios.get(
      `https://api.apify.com/v2/datasets/${datasetId}/items?token=${process.env.APIFY_API_TOKEN}`
    );

    return res.status(200).json(
      new BaseAPIResponse({
        statusCode: 200,
        data: { length: datasetRes.data.length, products: datasetRes.data },
        message: "Successfully scraped data",
      })
    );
  } catch (error) {
    CustomLogger.error(error);
    res
      .status(500)
      .json(new CustomApiError(500, error.message || "Something went wrong"));
  }
};

// =====================FASHIONGO=====================
export const scrapeVendorsFromFashionGo = async (req, res) => {};
