import * as puppeteer from "puppeteer";
import { Agent, Task, Crew } from "crewai-js";
import { logger } from "../../../utils/logger/logger.js";
export class ScraperService {
  constructor () {
    this.browser = null;
    this.initializeCrew();
  }

  initializeCrew() {
    // Define agents for the scraping crew
    this.scraperAgent = new Agent({
      name: "Web Scraper Specialist",
      role: "Web Scraper Specialist",
      goal: "Extract comprehensive product information from Alibaba product pages",
      backstory: `You are an expert web scraper with deep knowledge of e-commerce platforms.
                  You specialize in extracting structured product data from Alibaba listings,
                  including product details, pricing, supplier information, and specifications.`,
      llm: "gpt-3.5-turbo",
      verbose: false,
    });

    this.analyzerAgent = new Agent({
      name: "Data Analyzer",
      role: "Data Analyzer",
      goal: "Clean, structure, and validate scraped product data",
      backstory: `You are a data analyst who specializes in cleaning and structuring
                  e-commerce product data. You ensure data quality and consistency.`,
      llm: "gpt-3.5-turbo",
      verbose: false,
    });

    // Define tasks
    this.scrapeTask = new Task({
      description: `Extract all relevant product information from the given Alibaba URL including:
                   - Product title and description
                   - Price information (MOQ, unit price, price ranges)
                   - Supplier information (company name, location, ratings)
                   - Product specifications and features
                   - Images and videos
                   - Shipping and delivery information
                   - Customer reviews and ratings
                   - Product variations and options`,
      agent: this.scraperAgent,
      outputFormat: "raw",
    });

    this.analyzeTask = new Task({
      description: `Clean and structure the scraped data into a consistent JSON format:
                   - Validate and normalize price information
                   - Clean and format text fields
                   - Organize specifications into categories
                   - Extract and validate contact information
                   - Format dates and numerical values
                   - Remove duplicates and irrelevant data`,
      agent: this.analyzerAgent,
      outputFormat: "raw",
    });

    // Create the crew
    this.scrapingCrew = new Crew({
      name: "Alibaba Scraping Crew",
      agents: [this.scraperAgent, this.analyzerAgent],
      tasks: [this.scrapeTask, this.analyzeTask],
      verbose: false,
    });
  }

  async getBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: process.env.PUPPETEER_HEADLESS === "false" ? false : "new",
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-blink-features=AutomationControlled",
          "--disable-features=VizDisplayCompositor",
          "--disable-web-security",
          "--disable-features=site-per-process",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--single-process",
          "--disable-gpu",
        ],
        defaultViewport: null,
        ignoreDefaultArgs: ["--enable-automation"],
        ignoreHTTPSErrors: true,
      });
    }
    return this.browser;
  }

  async scrapeAlibabaProduct(url) {
    try {
      logger.info(`Scraping Alibaba product: ${url}`);

      // Validate URL
      if (!this.isValidAlibabaUrl(url)) {
        throw new Error("Invalid Alibaba URL provided");
      }

      // Get page content using Puppeteer
      const pageContent = await this.getPageContent(url);

      // Check if we have a valid OpenAI API key for CrewAI
      const hasValidApiKey =
        process.env.OPENAI_API_KEY &&
        process.env.OPENAI_API_KEY !== "your_openai_api_key_here" &&
        process.env.OPENAI_API_KEY.length > 10;

      let structuredData;

      if (hasValidApiKey) {
        logger.info("Using CrewAI for intelligent data extraction...");

        // Update agent goals with the specific URL and content
        this.scraperAgent.goal = `Extract comprehensive product information from this Alibaba URL: ${url}.
          Page title: ${pageContent.title}
          Page content: ${pageContent.html?.substring(0, 3000) || "No HTML content"}...`;

        this.analyzerAgent.goal = `Clean and structure the scraped product data into a consistent JSON format for the Alibaba product at: ${url}`;

        try {
          // Execute the crew to process the content
          const crewResults = await this.scrapingCrew.kickoff();

          structuredData = {
            url: url,
            scrapedAt: new Date().toISOString(),
            source: "alibaba",
            processingMethod: "crewai",
            data: crewResults || {
              title: pageContent.title || "No title found",
              description:
                "CrewAI processing completed but no results returned",
              rawContent:
                pageContent.html?.substring(0, 500) + "..." ||
                "No content available",
            },
            pageInfo: {
              finalUrl: pageContent.url,
              readyState: pageContent.readyState,
              extractedAt: pageContent.timestamp,
            },
          };

          logger.info("CrewAI processing completed successfully");
        } catch (crewError) {
          logger.warn(
            "CrewAI processing failed, falling back to basic extraction:",
            crewError.message
          );

          // Fallback to basic extraction
          structuredData = await this.basicDataExtraction(pageContent, url);
        }
      } else {
        logger.info(
          "No valid OpenAI API key found, using basic data extraction..."
        );

        // Use basic extraction without CrewAI
        structuredData = await this.basicDataExtraction(pageContent, url);
      }

      logger.info("Scraping completed successfully");
      return structuredData;
    } catch (error) {
      const errorInfo = this.categorizeError(error);
      logger.error("Error in scrapeAlibabaProduct:", {
        message: error.message,
        type: errorInfo.type,
        category: errorInfo.category,
        suggestion: errorInfo.suggestion,
        url: url,
      });

      // Enhance error with categorization info
      const enhancedError = new Error(error.message);
      enhancedError.type = errorInfo.type;
      enhancedError.category = errorInfo.category;
      enhancedError.suggestion = errorInfo.suggestion;
      enhancedError.originalError = error;

      throw enhancedError;
    }
  }

  async scrapeThomasnetProduct(url) {
    const browser = await this.getBrowser();
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: "domcontentloaded" });
    const data = await page.evaluate(() => {
      return document.body.innerHTML;
    });
    return data;
  }

  async getPageContent(url, retryCount = 0) {
    const maxRetries = 3;
    const baseTimeout = parseInt(process.env.PUPPETEER_TIMEOUT) || 60000; // Increased default timeout
    const timeout = baseTimeout + retryCount * 15000; // Increase timeout with each retry

    let page;
    try {
      const browser = await this.getBrowser();
      page = await browser.newPage();

      // Enhanced anti-bot detection measures
      await page.evaluateOnNewDocument(() => {
        // Remove webdriver property
        Object.defineProperty(navigator, "webdriver", {
          get: () => undefined,
        });

        // Mock plugins
        Object.defineProperty(navigator, "plugins", {
          get: () => [1, 2, 3, 4, 5],
        });

        // Mock languages
        Object.defineProperty(navigator, "languages", {
          get: () => ["en-US", "en"],
        });
      });

      // Set realistic user agent
      await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      );

      // Set viewport with some randomization
      const viewportWidth = 1366 + Math.floor(Math.random() * 200);
      const viewportHeight = 768 + Math.floor(Math.random() * 200);
      await page.setViewport({ width: viewportWidth, height: viewportHeight });

      // Set additional headers
      await page.setExtraHTTPHeaders({
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        DNT: "1",
        Connection: "keep-alive",
        "Upgrade-Insecure-Requests": "1",
      });

      logger.info(
        `Attempting to load page (attempt ${retryCount + 1}/${maxRetries + 1}) with timeout: ${timeout}ms`
      );

      // Try multiple navigation strategies
      let navigationSuccess = false;
      let content = null;

      // Strategy 1: Try networkidle0 first
      try {
        await page.goto(url, {
          waitUntil: "networkidle0",
          timeout: timeout,
        });
        navigationSuccess = true;
        logger.info("Navigation successful with networkidle0");
      } catch (error) {
        logger.warn(
          "networkidle0 failed, trying domcontentloaded:",
          error.message
        );

        // Strategy 2: Try domcontentloaded
        try {
          await page.goto(url, {
            waitUntil: "domcontentloaded",
            timeout: timeout,
          });
          navigationSuccess = true;
          logger.info("Navigation successful with domcontentloaded");
        } catch (error2) {
          logger.warn("domcontentloaded failed, trying load:", error2.message);

          // Strategy 3: Try basic load
          await page.goto(url, {
            waitUntil: "load",
            timeout: timeout,
          });
          navigationSuccess = true;
          logger.info("Navigation successful with load");
        }
      }

      if (navigationSuccess) {
        // Wait for content to load with random delay
        const waitTime = 2000 + Math.floor(Math.random() * 3000);
        await new Promise((resolve) => setTimeout(resolve, waitTime));

        // Try to wait for specific Alibaba elements
        try {
          await page.waitForSelector("body", { timeout: 5000 });
          logger.info("Body element found");
        } catch (error) {
          logger.warn("Body selector timeout, continuing anyway");
        }

        // Extract page content
        content = await page.evaluate(() => {
          return {
            title: document.title,
            html: document.documentElement.outerHTML,
            url: window.location.href,
            readyState: document.readyState,
            timestamp: new Date().toISOString(),
          };
        });

        logger.info(
          `Successfully extracted content. Title: ${content.title?.substring(0, 100)}...`
        );
        return content;
      }
    } catch (error) {
      logger.error(
        `Error getting page content (attempt ${retryCount + 1}):`,
        error.message
      );

      if (retryCount < maxRetries) {
        logger.info(`Retrying in ${(retryCount + 1) * 2} seconds...`);
        await new Promise((resolve) =>
          setTimeout(resolve, (retryCount + 1) * 2000)
        );

        // Close the failed page
        if (page) {
          try {
            await page.close();
          } catch (closeError) {
            logger.warn("Error closing page:", closeError.message);
          }
        }

        return this.getPageContent(url, retryCount + 1);
      }

      throw new Error(
        `Failed to load page after ${maxRetries + 1} attempts: ${error.message}`
      );
    } finally {
      if (page) {
        try {
          await page.close();
        } catch (closeError) {
          logger.warn(
            "Error closing page in finally block:",
            closeError.message
          );
        }
      }
    }
  }

  isValidAlibabaUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.includes("alibaba.com");
    } catch (error) {
      return false;
    }
  }

  async basicDataExtraction(pageContent, url) {
    logger.info("Performing basic data extraction without AI...");

    try {
      // Use Cheerio for basic HTML parsing
      const cheerio = require("cheerio");
      const $ = cheerio.load(pageContent.html || "");

      // Extract basic product information
      const title =
        pageContent.title ||
        $("title").text() ||
        $("h1").first().text() ||
        "No title found";

      // Try to extract price information
      const priceSelectors = [
        ".price",
        ".product-price",
        '[class*="price"]',
        "[data-price]",
        ".cost",
        ".amount",
      ];
      let price = "Price not found";
      for (const selector of priceSelectors) {
        const priceText = $(selector).first().text().trim();
        if (priceText && priceText.length > 0) {
          price = priceText;
          break;
        }
      }

      // Try to extract description
      const descSelectors = [
        ".description",
        ".product-description",
        '[class*="desc"]',
        ".details",
        ".product-details",
        ".summary",
      ];
      let description = "Description not found";
      for (const selector of descSelectors) {
        const descText = $(selector).first().text().trim();
        if (descText && descText.length > 50) {
          description =
            descText.substring(0, 500) + (descText.length > 500 ? "..." : "");
          break;
        }
      }

      // Try to extract supplier information
      const supplierSelectors = [
        ".supplier",
        ".company",
        '[class*="supplier"]',
        ".vendor",
        ".seller",
        ".manufacturer",
      ];
      let supplier = "Supplier not found";
      for (const selector of supplierSelectors) {
        const supplierText = $(selector).first().text().trim();
        if (supplierText && supplierText.length > 0) {
          supplier = supplierText;
          break;
        }
      }

      // Extract images
      const images = [];
      $("img").each((i, elem) => {
        const src = $(elem).attr("src");
        const alt = $(elem).attr("alt");
        if (src && !src.includes("data:image") && src.length > 10) {
          images.push({
            url: src.startsWith("http") ? src : `https:${src}`,
            alt: alt || "Product image",
          });
        }
      });

      return {
        url: url,
        scrapedAt: new Date().toISOString(),
        source: "alibaba",
        processingMethod: "basic",
        data: {
          title: title,
          description: description,
          price: price,
          supplier: supplier,
          images: images.slice(0, 10), // Limit to first 10 images
          rawContent:
            pageContent.html?.substring(0, 500) + "..." ||
            "No content available",
        },
        pageInfo: {
          finalUrl: pageContent.url,
          readyState: pageContent.readyState,
          extractedAt: pageContent.timestamp,
        },
      };
    } catch (error) {
      logger.error("Error in basic data extraction:", error);

      // Fallback to minimal data
      return {
        url: url,
        scrapedAt: new Date().toISOString(),
        source: "alibaba",
        processingMethod: "minimal",
        data: {
          title: pageContent.title || "No title found",
          description: "Basic extraction failed",
          rawContent:
            pageContent.html?.substring(0, 500) + "..." ||
            "No content available",
        },
        pageInfo: {
          finalUrl: pageContent.url,
          readyState: pageContent.readyState,
          extractedAt: pageContent.timestamp,
        },
      };
    }
  }

  categorizeError(error) {
    const message = error.message.toLowerCase();

    if (message.includes("timeout") || message.includes("navigation timeout")) {
      return {
        type: "TIMEOUT_ERROR",
        category: "Network",
        suggestion:
          "The page took too long to load. This might be due to slow network or the page being blocked.",
      };
    }

    if (
      message.includes("net::err_name_not_resolved") ||
      message.includes("net::err_internet_disconnected")
    ) {
      return {
        type: "NETWORK_ERROR",
        category: "Network",
        suggestion:
          "Network connectivity issue. Check your internet connection.",
      };
    }

    if (message.includes("403") || message.includes("forbidden")) {
      return {
        type: "ACCESS_DENIED",
        category: "Authorization",
        suggestion:
          "Access denied. The website might be blocking automated requests.",
      };
    }

    if (message.includes("404") || message.includes("not found")) {
      return {
        type: "PAGE_NOT_FOUND",
        category: "Content",
        suggestion: "The requested page was not found. Please check the URL.",
      };
    }

    if (message.includes("captcha") || message.includes("verification")) {
      return {
        type: "CAPTCHA_REQUIRED",
        category: "Security",
        suggestion:
          "The website requires human verification. Try again later or use a different approach.",
      };
    }

    return {
      type: "UNKNOWN_ERROR",
      category: "General",
      suggestion:
        "An unexpected error occurred. Please try again or contact support.",
    };
  }

  async getStatus() {
    try {
      return {
        service: "ScraperService",
        status: "operational",
        crewInitialized: !!this.scrapingCrew,
        browserReady: !!this.browser,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        service: "ScraperService",
        status: "error",
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}
