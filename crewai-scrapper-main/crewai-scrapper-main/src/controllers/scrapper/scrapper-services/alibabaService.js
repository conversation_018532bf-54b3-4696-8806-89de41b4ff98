import * as puppeteer from "puppeteer";
import { Agent, Task, Crew } from "crewai-js";
import { CustomLogger } from "../../../utils/logger/logger.js";

export class AlibabaService {
  constructor () {
    this.browser = null;
    this.initializeCrew();
  }

  // Add method to check API key
  checkApiKey() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      CustomLogger.warn("OpenAI API key is not set in environment variables");
      return false;
    }
    if (apiKey === "your_openai_api_key_here") {
      CustomLogger.warn("OpenAI API key is set to default value");
      return false;
    }
    if (apiKey.length < 10) {
      CustomLogger.warn("OpenAI API key appears to be invalid (too short)");
      return false;
    }
    return true;
  }

  initializeCrew() {
    try {
      // Only initialize if we have a valid API key
      if (!this.checkApiKey()) {
        CustomLogger.warn(
          "CrewAI initialization skipped - invalid or missing API key"
        );
        return;
      }

      // Product List Scraper Agent
      this.listScraperAgent = new Agent({
        name: "Product List Scraper",
        role: "Product List Extraction Specialist",
        goal: "Extract product listings from Alibaba search results pages",
        backstory: `You are an expert in parsing e-commerce search results pages.
                   You specialize in identifying and extracting multiple product listings
                   from Alibaba search pages, including pagination and list structures.`,
        llm: "gpt-3.5-turbo",
        verbose: true,
      });

      // Product Details Analyzer Agent
      this.productAnalyzerAgent = new Agent({
        name: "Product Details Analyzer",
        role: "Product Information Analyst",
        goal: "Clean and structure product listing data",
        backstory: `You are a data analyst who specializes in processing e-commerce product data.
                   You ensure data quality, normalize formats, and structure information consistently.`,
        llm: "gpt-3.5-turbo",
        verbose: true,
      });

      // Define tasks
      this.listExtractionTask = new Task({
        description: `Extract all product listings from the search results page including:
                     - Product URLs
                     - Basic product information (title, price, MOQ)
                     - Thumbnail images
                     - Quick overview details
                     - Supplier preview information
                     Handle multiple products per page and maintain list structure.`,
        agent: this.listScraperAgent,
        outputFormat: "raw",
      });

      this.dataAnalysisTask = new Task({
        description: `Process and structure the extracted product listings:
                     - Normalize price formats and currencies
                     - Standardize units and quantities
                     - Clean text fields and remove HTML artifacts
                     - Structure supplier information
                     - Validate data completeness
                     - Format into consistent JSON structure`,
        agent: this.productAnalyzerAgent,
        outputFormat: "raw",
      });

      // Create the crew
      this.alibabaCrew = new Crew({
        name: "Alibaba Products Scraping Crew",
        agents: [this.listScraperAgent, this.productAnalyzerAgent],
        tasks: [this.listExtractionTask, this.dataAnalysisTask],
        verbose: true,
      });
    } catch (error) {
      CustomLogger.error("Error in initializeCrew:", error);
      throw error;
    }
  }

  async getBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: process.env.PUPPETEER_HEADLESS === "false" ? false : "new",
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-blink-features=AutomationControlled",
          "--disable-web-security",
          "--disable-features=site-per-process",
          "--disable-dev-shm-usage",
        ],
        defaultViewport: null,
        ignoreDefaultArgs: ["--enable-automation"],
      });
    }
    return this.browser;
  }

  async scrapeProductsList(url) {
    try {
      CustomLogger.info(`Starting to scrape Alibaba products list: ${url}`);

      // Set default options
      const defaultOptions = {
        maxPages: 1,
        productsPerPage: 10,
        maxRetries: 1,
        delayBetweenPages: 2000,
      };

      const scrapeOptions = { ...defaultOptions };
      let allProducts = [];
      let currentPage = 1;
      let hasNextPage = true;

      while (hasNextPage && currentPage <= scrapeOptions.maxPages) {
        const pageUrl = this.constructPageUrl(url, currentPage);
        CustomLogger.info(`Scraping page ${currentPage} at URL: ${pageUrl}`);

        // Get page content
        const pageContent = await this.getPageContent(pageUrl);

        // Check for valid OpenAI API key and initialized crew
        const hasValidApiKey = this.checkApiKey() && this.alibabaCrew;
        let pageProducts = [];

        if (hasValidApiKey) {
          CustomLogger.info(
            "Using CrewAI for intelligent product extraction..."
          );

          try {
            // Update agent goals with the specific content
            this.listScraperAgent.goal = `Extract all product listings from this Alibaba search page: ${pageUrl}
              Page title: ${pageContent.title}
              Total products expected: ${scrapeOptions.productsPerPage}`;

            // Execute the crew to process the content
            const crewResults = await this.alibabaCrew.kickoff();
            pageProducts = Array.isArray(crewResults) ? crewResults : [];

            CustomLogger.info(
              `CrewAI found ${pageProducts.length} products on page ${currentPage}`
            );
          } catch (crewError) {
            CustomLogger.warn(
              `CrewAI processing failed for page ${currentPage}, falling back to basic extraction:`,
              crewError.message
            );
            pageProducts = await this.basicProductListExtraction(pageContent);
          }
        } else {
          CustomLogger.info(
            "Using basic product extraction method (CrewAI not available)..."
          );
          pageProducts = await this.basicProductListExtraction(pageContent);
        }

        // Add page metadata to each product
        pageProducts = pageProducts.map((product) => ({
          ...product,
          pageNumber: currentPage,
          searchUrl: url,
          extractedAt: new Date().toISOString(),
        }));

        // Add products from this page to overall results
        allProducts = [...allProducts, ...pageProducts];

        // Check if we should continue to next page
        hasNextPage = pageProducts.length >= scrapeOptions.productsPerPage / 2;

        if (hasNextPage && currentPage < scrapeOptions.maxPages) {
          // Add delay between pages
          const delay =
            scrapeOptions.delayBetweenPages + Math.floor(Math.random() * 1000);
          await new Promise((resolve) => setTimeout(resolve, delay));
          currentPage++;
        } else {
          break;
        }
      }

      return {
        success: true,
        searchUrl: url,
        scrapedAt: new Date().toISOString(),
        metadata: {
          totalProducts: allProducts.length,
          pagesScraped: currentPage,
          productsPerPage: scrapeOptions.productsPerPage,
          processingMethod:
            this.checkApiKey() && this.alibabaCrew ? "crewai" : "basic",
        },
        products: allProducts,
      };
    } catch (error) {
      CustomLogger.error("Error in scrapeProductsList:", error);
      throw new Error(`Failed to scrape products list: ${error.message}`);
    }
  }

  async getPageContent(url, retryCount = 0) {
    const maxRetries = 1;
    let page;

    try {
      const browser = await this.getBrowser();
      page = await browser.newPage();

      // Set realistic user agent
      await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      );

      // Set random viewport size
      const viewportWidth = 1366 + Math.floor(Math.random() * 200);
      const viewportHeight = 768 + Math.floor(Math.random() * 200);
      await page.setViewport({ width: viewportWidth, height: viewportHeight });

      // Set additional headers to look more like a real browser
      await page.setExtraHTTPHeaders({
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        Connection: "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Cache-Control": "max-age=0",
      });

      // Intercept request to modify headers
      await page.setRequestInterception(true);
      page.on("request", (request) => {
        const headers = request.headers();
        request.continue({
          ...request,
          headers: {
            ...headers,
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
          },
        });
      });

      // Navigate with longer timeout and different strategy
      CustomLogger.info(`Attempting to load URL: ${url}`);

      try {
        // First try with networkidle0
        await page.goto(url, {
          waitUntil: "networkidle0",
          timeout: 90000, // 90 seconds
        });
      } catch (navigationError) {
        CustomLogger.warn(
          "networkidle0 navigation failed, trying with domcontentloaded"
        );
        // If networkidle0 fails, try with domcontentloaded
        await page.goto(url, {
          waitUntil: "domcontentloaded",
          timeout: 60000, // 60 seconds
        });
      }

      // Wait for some time after navigation
      await page.waitForTimeout(5000);

      // Try different selectors for product container
      const selectors = [
        ".list-no-v2-main",
        ".fy23-search-card",
        ".organic-list",
        ".m-gallery-product-item-wrap",
      ];

      let foundSelector = false;
      for (const selector of selectors) {
        try {
          await page.waitForSelector(selector, { timeout: 5000 });
          CustomLogger.info(`Found selector: ${selector}`);
          foundSelector = true;
          break;
        } catch (error) {
          CustomLogger.warn(`Selector ${selector} not found`);
        }
      }

      if (!foundSelector) {
        CustomLogger.warn(
          "No product container selector found, proceeding anyway"
        );
      }

      // Scroll to load lazy content
      await this.autoScroll(page);

      // Extract page content
      const content = await page.evaluate(() => ({
        title: document.title,
        html: document.documentElement.outerHTML,
        url: window.location.href,
      }));

      return content;
    } catch (error) {
      CustomLogger.error(`Error loading page: ${error.message}`);
      if (retryCount < maxRetries) {
        CustomLogger.warn(
          `Retry ${retryCount + 1}/${maxRetries} for URL: ${url}`
        );
        await new Promise((resolve) =>
          setTimeout(resolve, (retryCount + 1) * 5000)
        );
        return this.getPageContent(url, retryCount + 1);
      }
      throw error;
    } finally {
      if (page) await page.close();
    }
  }

  // Helper method to scroll page and load lazy content
  async autoScroll(page) {
    await page.evaluate(async () => {
      await new Promise((resolve) => {
        let totalHeight = 0;
        const distance = 100;
        const timer = setInterval(() => {
          const scrollHeight = document.documentElement.scrollHeight;
          window.scrollBy(0, distance);
          totalHeight += distance;

          if (totalHeight >= scrollHeight) {
            clearInterval(timer);
            resolve();
          }
        }, 100);
      });
    });
  }

  async basicProductListExtraction(pageContent) {
    try {
      const cheerio = require("cheerio");
      const $ = cheerio.load(pageContent.html);
      const products = [];

      // Try multiple selectors for product cards
      const cardSelectors = [
        ".fy23-search-card",
        ".list-no-v2-item",
        ".organic-gallery-offer-outter",
        ".m-gallery-product-item-wrap",
      ];

      let foundProducts = false;
      for (const selector of cardSelectors) {
        $(selector).each((_, element) => {
          const $el = $(element);

          // Try multiple selectors for each field
          const title =
            $el.find(".elements-title-normal").text().trim() ||
            $el.find(".organic-gallery-offer__title").text().trim() ||
            $el.find(".title").text().trim();

          const priceText =
            $el.find(".elements-offer-price-normal").text().trim() ||
            $el.find(".organic-gallery-offer__price").text().trim() ||
            $el.find(".price").text().trim();

          const currencyText =
            $el.find(".elements-offer-price-normal__currency").text().trim() ||
            $el.find(".currency").text().trim();

          const moqText =
            $el.find(".element-offer-minorder-normal").text().trim() ||
            $el.find(".min-order").text().trim();

          const product = {
            title,
            price: {
              range: priceText,
              currency: currencyText,
            },
            moq: moqText,
            supplier: {
              name: $el.find('[class*="supplier-name"]').text().trim(),
              location: $el.find('[class*="supplier-location"]').text().trim(),
              rating: $el.find('[class*="supplier-rating"]').text().trim(),
            },
            image: {
              src: $el.find("img").attr("src") || "",
              alt: $el.find("img").attr("alt") || "",
            },
            productUrl: $el.find("a").first().attr("href") || "",
          };

          if (product.title) {
            products.push(product);
            foundProducts = true;
          }
        });

        if (foundProducts) {
          CustomLogger.info(`Found products using selector: ${selector}`);
          break;
        }
      }

      return products;
    } catch (error) {
      CustomLogger.error("Error in basic product list extraction:", error);
      return [];
    }
  }

  constructPageUrl(baseUrl, pageNumber) {
    const url = new URL(baseUrl);
    if (pageNumber > 1) {
      url.searchParams.set("page", pageNumber);
    }
    return url.toString();
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}
