import { StatusCodes } from "http-status-codes";
import { getHistoryRepo } from "../../../models/product/history.entity.js";
import { CustomApiError } from "../../utils/errors/index.js";
import { BaseAPIResponse } from "../../utils/responses/ApiResponse.js";

// export const saveSearchHistory = async (req, res) => {
//   try {
//     const { term } = req.body;
//     const userId = req.user.id;

//     if (!term) {
//       throw new CustomApiError(
//         StatusCodes.BAD_REQUEST,
//         "Search term is required"
//       );
//     }
//     const historyRepo = getHistoryRepo();

//     const newHistory = historyRepo.create({
//       term,
//       user: { id: userId },
//     });

//     await historyRepo.save(newHistory);
//     const response = new BaseAPIResponse({
//       statusCode: StatusCodes.OK,
//       data: {
//         message: "Search term successfully save",
//         newHistory,
//       },
//     });
//     res.status(response.statusCode).json(response);
//   } catch (err) {
//     res.status(err.statusCode || StatusCodes.INTERNAL_SERVER_ERROR).json({
//       status: "error",
//       message: err.message || "Internal Server Error",
//     });
//   }
// };

export const getSearchHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const historyRepo = getHistoryRepo();
    if (!userId) {
      throw new CustomApiError(
        StatusCodes.UNAUTHORIZED,
        "Unauthorized: User ID missing"
      );
    }
    const history = await historyRepo.find({
      where: { user: { id: userId } },
      order: { createdAt: "DESC" },
    });

    const response = new BaseAPIResponse({
      statusCode: StatusCodes.OK,
      data: {
        message: "Search history retrieved successfully",
        history,
      },
    });
    res.status(response.statusCode).json(response);
  } catch (err) {
    res.status(err.statusCode || StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: "error",
      message: err.message || "Internal Server Error",
    });
  }
};

export const deleteSearchHistory = async (req, res) => {
  const userId = req.user.id;
  const { id } = req.params;
  const historyRepo = getHistoryRepo();

  try {
    const entry = await historyRepo.findOne({
      where: { id, user: { id: userId } },
    });

    if (!entry) {
      throw new CustomApiError(StatusCodes.NOT_FOUND, "Search entry not found");
    }

    await historyRepo.remove(entry);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: "Search history entry deleted successfully",
    });
  } catch (err) {
    return res
      .status(err.statusCode || StatusCodes.INTERNAL_SERVER_ERROR)
      .json({
        success: false,
        message: err.message || "Failed to delete search entry",
      });
  }
};

export const clearSearchHistory = async (req, res) => {
  const userId = req.user.id;
  const historyRepo = getHistoryRepo();

  try {
    const result = await historyRepo.delete({ user: { id: userId } });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: "All search history cleared",
      deletedCount: result.affected || 0,
    });
  } catch (err) {
    return res
      .status(err.statusCode || StatusCodes.INTERNAL_SERVER_ERROR)
      .json({
        success: false,
        message: err.message || "Failed to clear search history",
      });
  }
};
