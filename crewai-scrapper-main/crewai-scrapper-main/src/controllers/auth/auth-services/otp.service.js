import * as bcrypt from "bcrypt";
import { getOtpRepo, OtpTypes } from "../../../../models/auth/index.js";
import { verifyOtpValidation } from "../auth-services/validations/verify-otp.validations.js";
import { CustomApiError } from "../../../utils/errors/CustomApiError.js";
import { StatusCodes } from "http-status-codes";
import { getUserRepo } from "../../../../models/user/user.entity.js";
import { CustomLogger } from "../../../utils/logger/logger.js";
import { generateAccessToken, generateRefreshToken } from "./auth.service.js";
import { VERIFICATION_EMAIL_TEMPLATE } from "../../../common/email-templates/email-verification.template.js";
import { SendEmail } from "../../../third-party/Resend/resend.service.js";

const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000);
};

export const validateUserOTP = async ({ email, otp }) => {
  const { error } = verifyOtpValidation.validate({
    email,
    otp,
  });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.context.key,
      message: detail.message,
    }));

    throw new CustomApiError(StatusCodes.BAD_REQUEST, errors[0]?.message);
  }
};

export const generateAndSaveVerificationCode = async (
  email,
  type = OtpTypes.SIGNUP_REQUEST
) => {
  try {
    const otp = generateVerificationCode().toString();
    const otpHash = await bcrypt.hash(otp, 10);

    const otpRepo = getOtpRepo();

    const existingVerificationCodes = await otpRepo.findOne({
      where: { email, type, isExpired: false },
    });

    if (existingVerificationCodes) {
      await otpRepo.update(existingVerificationCodes.id, { isExpired: true });
    }

    const otpExpiry = Number(process.env.OTP_EXPIRY) || 15;

    const newVerificationCode = otpRepo.create({
      email,
      otpHash,
      type,
      expiresAt: new Date(Date.now() + otpExpiry * 60 * 1000),
    });

    if (!newVerificationCode) {
      return {
        success: false,
        message: "Failed to generate verification code",
        data: null,
        error: "Failed to generate verification code",
      };
    }
    const newOtpSaved = await otpRepo.save(newVerificationCode);

    if (!newOtpSaved) {
      return {
        success: false,
        message: "Failed to save verification code",
        data: null,
        error: "Failed to save verification code",
      };
    }

    return {
      success: true,
      message: "Verification code saved successfully",
      data: {
        verificationCode: otp,
        verificationCodeId: newVerificationCode.id,
        otpExpiry,
      },
      error: null,
    };
  } catch (error) {
    CustomLogger.error(error);
    return {
      success: false,
      message: error.message,
      data: null,
      error: error,
    };
  }
};

export const verifyUser = async ({ email, otp }) => {
  const userRepo = getUserRepo();

  let user = await userRepo.findOne({ where: { email } });

  if (!user) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "Account not found !!");
  }

  if (user.isVerified) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Account already verified !!"
    );
  }

  const otpRepo = getOtpRepo();

  const otpData = await otpRepo.findOne({
    where: { email, isExpired: false },
  });

  if (!otpData) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "Invalid OTP !!");
  }

  const otpExpiry = new Date(otpData.expiresAt);
  if (otpData.isExpired || otpExpiry < new Date()) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "OTP expired !!");
  }

  const isValidCode = await bcrypt.compare(otp.toString(), otpData.otpHash);
  if (!isValidCode) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "Invalid OTP !!");
  }

  await otpRepo.update(
    { email, type: OtpTypes.SIGNUP_REQUEST },
    { isExpired: true }
  );

  await userRepo.update({ email }, { isVerified: true });

  const updatedUser = await userRepo.findOne({ where: { email } });

  const refreshToken = await generateRefreshToken({ userId: updatedUser.id });
  const accessToken = await generateAccessToken({
    userId: updatedUser.id,
    refreshToken: { id: refreshToken.id },
  });

  return {
    user: updatedUser,
    accessToken,
    refreshToken,
  };
};

//-----------------

export const resendOTPToUser = async (email) => {
  const userRepo = getUserRepo();

  const user = await userRepo.findOne({ where: { email } });

  if (!user) {
    throw new CustomApiError(StatusCodes.NOT_FOUND, "User not found");
  }

  if (user.isVerified) {
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "User already verified");
  }

  const otpResponse = await generateAndSaveVerificationCode(email);

  if (!otpResponse.success) {
    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      otpResponse.message
    );
  }

  console.log({ otp: otpResponse.data.verificationCode });
  const emailTemplate = VERIFICATION_EMAIL_TEMPLATE(
    user.name,
    otpResponse.data.verificationCode,
    otpResponse.data.otpExpiry
  );

  const sentEmail = await SendEmail(
    email,
    "Your OTP Code - Resend Request",
    emailTemplate
  );

  if (!sentEmail?.success) {
    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      "Failed to send OTP email"
    );
  }

  return {
    email,
    otpSent: true,
  };
};
