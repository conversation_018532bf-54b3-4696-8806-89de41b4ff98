import { SendEmail } from "../../../third-party/Resend/index.js";
import { generateAndSaveVerificationCode } from "./otp.service.js";
import {
  userLoginValidation,
  userRegisterValidation,
  forgetPasswordValidation,
  resetPasswordValidation,
} from "./validations/index.js";
import { StatusCodes } from "http-status-codes";
import { CustomApiError } from "../../../utils/errors/index.js";
import { getRoleRepo, getUserRepo } from "../../../../models/user/index.js";
import { VERIFICATION_EMAIL_TEMPLATE } from "../../../common/email-templates/email-verification.template.js";
import { FORGOT_PASSWORD_EMAIL_TEMPLATE } from "../../../common/email-templates/email-forgetPassword.template.js";
import * as bcrypt from "bcrypt";
import * as crypto from "crypto";
import { CustomLogger } from "../../../utils/logger/logger.js";
import jwt from "jsonwebtoken";
import { getAccessTokenRepo } from "../../../../models/auth/accessToken.entity.js";
import { getRefreshTokenRepo } from "../../../../models/auth/refreshToken.entity.js";
import { getOtpRepo, OtpTypes } from "../../../../models/auth/index.js";

// -------------------------------------------

export const validateUserRegister = async ({ name, email, password }) => {
  const { error } = userRegisterValidation.validate({
    name,
    email,
    password,
  });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.context.key,
      message: detail.message,
    }));

    throw new CustomApiError(StatusCodes.BAD_REQUEST, errors[0]?.message);
  }
};

export const generateHashedPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
};

export const savingUserInDb = async ({ name, email, password }) => {
  const userRepo = getUserRepo();
  const roleRepo = getRoleRepo();

  // get hashed password
  const hashedPassword = await generateHashedPassword(password);

  // Check if user already exists
  const existingUser = await userRepo.findOne({ where: { email } });

  // If user already exists and is verified, return error
  if (existingUser) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Account with this email is already in use !!"
    );
  }
  const defaultRole = await roleRepo.findOneBy({ value: "user" });
  if (!defaultRole)
    throw new CustomApiError(StatusCodes.BAD_REQUEST, "Role 'user' not found");
  // If user does not exist, create a new user
  const newUser = userRepo.create({
    name,
    email,
    password: hashedPassword,
    role: defaultRole,
  });
  await userRepo.save(newUser);

  return newUser;
};

export const sendVerficationEmail = async ({ email, name }) => {
  const { success, message, data } =
    await generateAndSaveVerificationCode(email);

  if (!success) {
    CustomLogger.error(
      "Error while generating and saving verification code: " + message
    );
    throw new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, message);
  }

  const emailTemplate = VERIFICATION_EMAIL_TEMPLATE(
    name,
    data.verificationCode,
    data.otpExpiry
  );

  console.log({ otp: data.verificationCode });

  const sentEmail = await SendEmail(
    email,
    "Your One-Time Password (OTP) for Verification",
    emailTemplate
  );

  if (!sentEmail?.success) {
    CustomLogger.error("Error while sending email: " + sentEmail.message);
    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      sentEmail.message
    );
  }
};

export const sendForgetPasswordEmail = async ({ email, name }) => {
  const { success, message, data } =
    await generateAndSaveVerificationCode(email);

  if (!success) {
    CustomLogger.error(
      "Error while generating and saving verification code: " + message
    );
    throw new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, message);
  }

  const emailTemplate = FORGOT_PASSWORD_EMAIL_TEMPLATE(
    name,
    data.verificationCode,
    30
  );
  console.log({ otp: data.verificationCode });
  const sentEmail = await SendEmail(
    email,
    "Your One-Time Password (OTP) for Forget Password",
    emailTemplate
  );

  if (!sentEmail?.success) {
    CustomLogger.error("Error while sending email: " + sentEmail.message);
    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      sentEmail.message
    );
  }
};

export const validateForgetPassword = async ({ email }) => {
  const { error } = forgetPasswordValidation.validate({ email });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.context.key,
      message: detail.message,
    }));

    throw new CustomApiError(StatusCodes.BAD_REQUEST, errors[0]?.message);
  }
};

export const handleForgetPassword = async ({ email }) => {
  const userRepo = getUserRepo();
  const user = await userRepo.findOne({
    where: { email },
    select: {
      id: true,
      email: true,
      name: true,
      isVerified: true,
    },
  });

  if (!user) {
    throw new CustomApiError(
      StatusCodes.NOT_FOUND,
      "No account found with this email address"
    );
  }

  if (!user.isVerified) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Please verify your email address first"
    );
  }

  // Generate and save OTP
  const { success, message, data } = await generateAndSaveVerificationCode(
    email,
    OtpTypes.FORGET_PASSWORD_REQUEST
  );

  if (!success) {
    CustomLogger.error(
      "Error while generating and saving verification code: " + message
    );
    throw new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, message);
  }

  // Log OTP for debugging
  console.log("Generated OTP for forget password:", {
    email: user.email,
    otp: data.verificationCode,
    expiryMinutes: data.otpExpiry,
  });

  // Send forget password email
  const emailTemplate = FORGOT_PASSWORD_EMAIL_TEMPLATE(
    user.name,
    data.verificationCode,
    data.otpExpiry
  );

  const sentEmail = await SendEmail(
    email,
    "Reset Your Password - OTP",
    emailTemplate
  );

  if (!sentEmail?.success) {
    CustomLogger.error("Error while sending email: " + sentEmail.message);
    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      sentEmail.message
    );
  }

  return {
    success: true,
    message: "Password reset instructions sent to your email",
    data: {
      email: user.email,
      otpSent: true,
    },
  };
};

// -----------------------------------------

const getHashToken = (token) => {
  return crypto.createHash("sha256").update(token).digest("hex");
};

export const generateAccessToken = async (payload) => {
  const token = jwt.sign(
    {
      userId: payload.userId,
      refreshTokenId: payload.refreshToken.id,
    },
    process.env.JWT_SECRET_ACCESS_TOKEN,
    {
      expiresIn: process.env.JWT_ACCESS_TOKEN_EXPIRY,
    }
  );

  const hashedToken = getHashToken(token);
  const accessTokenRepo = getAccessTokenRepo();
  const accessTokenEntry = accessTokenRepo.create({
    hashedToken,
    user: { id: payload.userId },
    isExpired: false,
    refreshToken: { id: payload.refreshToken.id },
  });
  await accessTokenRepo.save(accessTokenEntry);

  return token;
};

export const generateRefreshToken = async (payload) => {
  const token = jwt.sign(
    { userId: payload.userId },
    process.env.JWT_SECRET_REFRESH_TOKEN,
    {
      expiresIn: process.env.JWT_REFRESH_TOKEN_EXPIRY,
    }
  );

  const hashedToken = getHashToken(token);
  const refreshTokenRepo = getRefreshTokenRepo();
  const refreshTokenEntry = refreshTokenRepo.create({
    hashedToken,
    token,
    user: { id: payload.userId },
    isExpired: false,
  });
  const savedRefreshToken = await refreshTokenRepo.save(refreshTokenEntry);

  return {
    token,
    id: savedRefreshToken.id,
  };
};

// -------------------------------------------

export const validateUserLogin = async ({ email, password }) => {
  const { error } = userLoginValidation.validate({
    email,
    password,
  });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.context.key,
      message: detail.message,
    }));

    throw new CustomApiError(StatusCodes.BAD_REQUEST, errors[0]?.message);
  }
};

export const login = async (payload) => {
  const { email, password } = payload;

  const userRepo = getUserRepo();
  const user = await userRepo.findOne({
    where: { email },
    select: {
      id: true,
      email: true,
      name: true,
      password: true,
      isVerified: true,
    },
    relations: ["role"],
  });

  if (!user) {
    throw new CustomApiError(400, "Email or password is incorrect");
  }

  if (!user.isVerified) {
    const otpResponse = await generateAndSaveVerificationCode(user.email);
    await sendVerficationEmail({
      email: user.email,
      name: user.email,
      otp: otpResponse.data.verificationCode,
    });
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Email not verified. A new OTP has been sent to your email."
    );
  }

  // Need to add resend otp logic here......

  const isPasswordValid = await bcrypt.compare(password, user.password);

  if (!isPasswordValid) {
    throw new CustomApiError(400, "Email or password is incorrect");
  }

  const refreshToken = await generateRefreshToken({ userId: user.id });
  const accessToken = await generateAccessToken({
    userId: user.id,
    refreshToken: { id: refreshToken.id },
  });

  return { accessToken, user };
};

// -------------------------------------------

export const validateToken = async (token, tokenType) => {
  let decoded;

  try {
    const secret =
      tokenType === "accessToken"
        ? process.env.JWT_SECRET_ACCESS_TOKEN
        : process.env.JWT_SECRET_REFRESH_TOKEN;

    decoded = jwt.verify(token, secret);
  } catch (error) {
    CustomLogger.error(error);

    return {
      success: false,
      user: null,
      tokenExpired: true,
    };
  }

  if (!decoded?.userId) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Invalid tokens provided !!"
    );
  }

  const hashedToken = getHashToken(token);

  let tokenRepo = null;
  if (tokenType === "accessToken") {
    tokenRepo = getAccessTokenRepo();
  } else {
    tokenRepo = getRefreshTokenRepo();
  }

  if (!tokenRepo) {
    throw new CustomApiError("Please provide token type !!");
  }

  const tokenEntry = await tokenRepo.findOne({
    where: {
      hashedToken,
    },
    relations: ["user"],
  });

  if (!tokenEntry) {
    throw new CustomApiError(StatusCodes.UNAUTHORIZED, "Tokens not found !!");
  }

  if (tokenEntry.isExpired === true) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      `${tokenType} is expired !!`
    );
  }

  const userRepo = getUserRepo();

  const user = await userRepo.findOne({
    where: {
      id: decoded.userId,
      isVerified: true,
    },
  });

  if (!user) {
    await tokenRepo.update({ hashedToken }, { isExpired: true });
    throw new CustomApiError(StatusCodes.UNAUTHORIZED, "User not found !!");
  }

  // Step 5: Check if token belongs to the correct user
  if (tokenEntry.user.id !== user.id) {
    throw new CustomApiError(StatusCodes.UNAUTHORIZED, "Invalid Session !!");
  }

  return {
    success: true,
    user,
  };
};

// -------------------------------------------

export const checkAndExpireTokens = async (accessToken) => {
  if (!accessToken) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Please provide accessToken !!"
    );
  }

  // Get token repositories
  const accessTokenRepo = getAccessTokenRepo();
  const refreshTokenRepo = getRefreshTokenRepo();

  // Decode the access token to get the refresh token ID
  const decoded = jwt.decode(accessToken);
  if (!decoded?.refreshTokenId) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Invalid access token format"
    );
  }

  // Find both tokens
  const [accessTokenData, refreshTokenData] = await Promise.all([
    accessTokenRepo.findOne({
      where: { hashedToken: getHashToken(accessToken) },
      relations: ["user"],
    }),
    refreshTokenRepo.findOne({
      where: { id: decoded.refreshTokenId },
      relations: ["user"],
    }),
  ]);

  if (!accessTokenData) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Access token not found"
    );
  }

  if (!refreshTokenData) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Refresh token not found"
    );
  }

  // Expire both tokens
  await Promise.all([
    accessTokenRepo.update(
      { hashedToken: getHashToken(accessToken) },
      { isExpired: true }
    ),
    refreshTokenRepo.update(
      { id: decoded.refreshTokenId },
      { isExpired: true }
    ),
  ]);

  // Get updated token records after expiration
  const [expiredAccessToken, expiredRefreshToken] = await Promise.all([
    accessTokenRepo.findOne({
      where: { hashedToken: getHashToken(accessToken) },
      relations: ["user"],
    }),
    refreshTokenRepo.findOne({
      where: { id: decoded.refreshTokenId },
      relations: ["user"],
    }),
  ]);
};

// -------------------------------------------

export const checkAndgenerateNewTokens = async ({ accessToken }) => {
  // Get token repositories
  const accessTokenRepo = getAccessTokenRepo();
  const refreshTokenRepo = getRefreshTokenRepo();

  if (!accessToken) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Access token is required"
    );
  }

  const hashedToken = getHashToken(accessToken);

  const accessTokenEntry = await accessTokenRepo.findOne({
    where: {
      hashedToken,
    },
    relations: ["user", "refreshToken"],
  });

  if (!accessTokenEntry) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Access token not found !!"
    );
  }

  // Decode the access token to check if it's expired
  const decoded = jwt.decode(accessToken);
  if (!decoded) {
    throw new CustomApiError(StatusCodes.UNAUTHORIZED, "Invalid access token");
  }

  const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds

  if (decoded.exp <= currentTime) {
    accessTokenEntry.isExpired = true;
    await accessTokenRepo.save(accessTokenEntry);

    // Access token is expired, proceed to generate new tokens
    const userRepo = getUserRepo();

    const accessTokenUser = await userRepo.findOne({
      where: {
        id: accessTokenEntry.user.id,
        isVerified: true,
      },
    });

    if (!accessTokenUser) {
      await accessTokenRepo.update({ hashedToken }, { isExpired: true });
      throw new CustomApiError(StatusCodes.UNAUTHORIZED, "User not found !!");
    }

    const refreshToken = await refreshTokenRepo.findOne({
      where: { id: accessTokenEntry.refreshToken.id },
    });

    if (!refreshToken) {
      throw new CustomApiError(
        StatusCodes.UNAUTHORIZED,
        "Refresh token not found !!"
      );
    }

    // Validate the refresh token
    let refreshTokenDecoded;
    try {
      refreshTokenDecoded = jwt.verify(
        refreshToken.token,
        process.env.JWT_SECRET_REFRESH_TOKEN
      );
    } catch (error) {
      throw new CustomApiError(
        StatusCodes.UNAUTHORIZED,
        "Invalid or expired refresh token !!"
      );
    }

    if (!refreshTokenDecoded?.userId) {
      throw new CustomApiError(
        StatusCodes.UNAUTHORIZED,
        "Invalid refresh token payload !!"
      );
    }

    if (refreshToken.isExpired) {
      throw new CustomApiError(
        StatusCodes.UNAUTHORIZED,
        "Refresh token is expired !!"
      );
    }

    // Generate new tokens
    const newAccessToken = await generateAccessToken({
      userId: accessTokenUser.id,
      refreshToken,
    });

    return {
      newAccessToken,
    };
  } else {
    // Access token is still active, throw an error
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Access token is still active !!"
    );
  }
};

export const validateResetPassword = async ({ email, otp, newPassword }) => {
  const { error } = resetPasswordValidation.validate({
    email,
    otp,
    newPassword,
  });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.context.key,
      message: detail.message,
    }));

    throw new CustomApiError(StatusCodes.BAD_REQUEST, errors[0]?.message);
  }
};

export const handleResetPassword = async ({ email, otp, newPassword }) => {
  const userRepo = getUserRepo();
  const user = await userRepo.findOne({
    where: { email },
    select: {
      id: true,
      email: true,
      name: true,
      isVerified: true,
    },
  });

  if (!user) {
    throw new CustomApiError(
      StatusCodes.NOT_FOUND,
      "No account found with this email address"
    );
  }

  if (!user.isVerified) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Please verify your email address first"
    );
  }

  // Verify OTP
  const otpRepo = getOtpRepo();
  const otpData = await otpRepo.findOne({
    where: {
      email,
      isExpired: false,
      type: OtpTypes.FORGET_PASSWORD_REQUEST,
    },
  });

  if (!otpData) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Invalid or expired OTP. Please request a new one."
    );
  }

  const otpExpiry = new Date(otpData.expiresAt);
  if (otpData.isExpired || otpExpiry < new Date()) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "OTP has expired. Please request a new one."
    );
  }

  const isValidCode = await bcrypt.compare(otp.toString(), otpData.otpHash);
  if (!isValidCode) {
    throw new CustomApiError(
      StatusCodes.BAD_REQUEST,
      "Invalid OTP. Please try again."
    );
  }

  // Generate new password hash
  const hashedPassword = await generateHashedPassword(newPassword);

  // Update password and expire OTP
  await userRepo.update({ email }, { password: hashedPassword });
  await otpRepo.update(
    { email, type: OtpTypes.FORGET_PASSWORD_REQUEST },
    { isExpired: true }
  );

  return {
    success: true,
    message: "Password has been reset successfully",
  };
};
