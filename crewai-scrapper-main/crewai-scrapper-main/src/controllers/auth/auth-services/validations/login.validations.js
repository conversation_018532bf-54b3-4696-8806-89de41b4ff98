import Joi from "joi";

export const userLoginValidation = Joi.object({
  email: Joi.string().trim().email().required().messages({
    "string.empty": "Please enter your email address.",
    "string.email": "This email address is not valid.",
    "any.required": "Email address is required.",
  }),

  password: Joi.string()
    .pattern(
      new RegExp(
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,30}$"
      )
    )
    .required()
    .messages({
      "string.empty": "Please enter your password.",
      "string.pattern.base":
        "Password must be 8–30 characters and include uppercase, lowercase, number, and special character.",
      "string.min": "Password must be at least 8 characters long.",
      "string.max": "Password must not exceed 30 characters.",
      "any.required": "Password is required.",
    }),
});
