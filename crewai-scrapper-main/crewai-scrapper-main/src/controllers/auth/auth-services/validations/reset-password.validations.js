import Jo<PERSON> from "joi";

export const resetPasswordValidation = Joi.object({
  email: Joi.string().trim().email().required().messages({
    "string.empty": "Please enter your email address.",
    "string.email": "This email address is not valid.",
    "any.required": "Email address is required.",
  }),

  otp: Joi.string()
    .length(6)
    .pattern(/^[0-9]{6}$/)
    .required()
    .messages({
      "string.empty": "Please enter the OTP sent to your email.",
      "string.length": "OTP must be 6 digits long.",
      "string.pattern.base": "OTP should only contain numbers.",
      "any.required": "OTP is required.",
    }),

  newPassword: Joi.string()
    .pattern(
      new RegExp(
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,30}$"
      )
    )
    .required()
    .messages({
      "string.empty": "Please enter your new password.",
      "string.pattern.base":
        "Password must be 8-30 characters and include: 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character (e.g. @, $, !).",
      "string.min": "Password must be at least 8 characters.",
      "string.max": "Password must not be longer than 30 characters.",
      "any.required": "Password is required.",
    }),
});
