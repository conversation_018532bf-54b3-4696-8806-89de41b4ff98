import Joi from "joi";

export const verifyOtpValidation = Joi.object({
  email: Joi.string().trim().email().required().messages({
    "string.empty": "Please enter your email address.",
    "string.email": "This email address is not valid.",
    "any.required": "Email address is required.",
  }),

  otp: Joi.string()
    .length(6)
    .pattern(/^[A-Za-z0-9]{6}$/)
    .required()
    .messages({
      "string.empty": "Please enter the OTP sent to your email.",
      "string.length": "OTP must be 6 characters long.",
      "string.pattern.base": "OTP should only contain letters and numbers.",
      "any.required": "OTP is required.",
    }),
});
