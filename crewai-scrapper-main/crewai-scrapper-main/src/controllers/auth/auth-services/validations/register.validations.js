import Joi from "joi";

export const userRegisterValidation = Joi.object({
  name: Joi.string()
    .trim()
    .min(3)
    .max(30)
    .pattern(/^[a-zA-Z]+(?: [a-zA-Z]+)*$/)
    .required()
    .messages({
      "string.empty": "Please enter your name.",
      "string.min": "Name must be at least 3 characters long.",
      "string.max": "Name must be 30 characters or less.",
      "string.pattern.base":
        "Name should only contain letters and spaces (e.g. <PERSON>).",
      "any.required": "Name is required.",
    }),

  email: Joi.string().trim().email().required().messages({
    "string.empty": "Please enter your email address.",
    "string.email": "That doesn’t look like a valid email. Please check again.",
    "any.required": "Email is required.",
  }),

  password: Joi.string()
    .pattern(
      new RegExp(
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,30}$"
      )
    )
    .required()
    .messages({
      "string.empty": "Please enter a password.",
      "string.pattern.base":
        "Password must be 8-30 characters and include: 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character (e.g. @, $, !).",
      "string.min": "Password must be at least 8 characters.",
      "string.max": "Password must not be longer than 30 characters.",
      "any.required": "Password is required.",
    }),
});
