import { StatusCodes } from "http-status-codes";
import { BaseAPIResponse } from "../../utils/responses/index.js";
import {
  login,
  savingUserInDb,
  sendVerficationEmail,
  validateUserLogin,
  validateUserRegister,
  checkAndgenerateNewTokens,
  checkAndExpireTokens,
  validateForgetPassword,
  handleForgetPassword,
  validateResetPassword,
  handleResetPassword,
} from "./auth-services/auth.service.js";
import {
  validateUserOTP,
  verifyUser,
  resendOTPToUser,
} from "./auth-services/otp.service.js";
import { HttpStatusCode } from "axios";
import { CustomApiError } from "../../utils/errors/CustomApiError.js";
// -------------------------

export const registerUser = async (req, res) => {
  const { name, email, password } = req.body;

  // 1. Validate user input
  await validateUserRegister({ name, email, password });

  // 2. Save user in the database
  const user = await savingUserInDb({ name, email, password });

  // 3. Send verification email
  await sendVerficationEmail({ email, name });

  // 4. Respond with success (you may want to avoid sending sensitive info)
  const resp = new BaseAPIResponse({
    statusCode: StatusCodes.OK,
    message: "Registeration OTP sent successfully !!",
  });

  return res.status(StatusCodes.CREATED).json(resp);
};

// -------------------------

export const verifyOTP = async (req, res) => {
  const { email, otp } = req.body;

  await validateUserOTP({ email, otp });

  const { user, accessToken } = await verifyUser({ email, otp });

  return res.status(StatusCodes.OK).json(
    new BaseAPIResponse({
      data: {
        user,
        accessToken,
      },
      statusCode: StatusCodes.OK,
      message: "User Verified Successfully !!",
    })
  );
};

//--------------------------------------

export const resendOtp = async (req, res, next) => {
  try {
    const { email } = req.body;

    if (!email) {
      throw new CustomApiError(StatusCodes.BAD_REQUEST, "Email is required");
    }

    const result = await resendOTPToUser(email);

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: "OTP sent successfully!",
        data: result,
      })
    );
  } catch (error) {
    next(error);
  }
};

// -------------------------

export const loginUser = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    await validateUserLogin({ email, password });

    // All login logic is handled by the service
    const resp = await login({ email, password });

    return res.status(HttpStatusCode.Ok).json(
      new BaseAPIResponse({
        statusCode: HttpStatusCode.Ok,
        message: "logged in successfully",
        data: resp,
      })
    );
  } catch (error) {
    next(error);
  }
};

// -------------------------
export const logout = async (req, res) => {
  const accessToken =
    req.headers?.authorization?.split(" ")[1] ||
    req.headers?.Authorization?.split(" ")[1];

  await checkAndExpireTokens(accessToken);

  return res.status(StatusCodes.OK).json(
    new BaseAPIResponse({
      statusCode: StatusCodes.OK,
      data: null,
      message: "Logged out successfully",
    })
  );
};

// -------------------------

export const getNewAccessToken = async (req, res) => {
  const accessToken =
    req.headers?.authorization?.split(" ")[1] ||
    req.headers?.Authorization?.split(" ")[1];

  const { newAccessToken } = await checkAndgenerateNewTokens({
    accessToken,
  });

  return res.status(StatusCodes.OK).json(
    new BaseAPIResponse({
      statusCode: StatusCodes.OK,
      data: {
        accessToken: newAccessToken,
      },
      message: "Tokens refreshed successfully",
    })
  );
};

// -------------------------

export const forgetPassword = async (req, res, next) => {
  try {
    const { email } = req.body;

    // Validate the email
    await validateForgetPassword({ email });

    // Handle forget password logic
    const result = await handleForgetPassword({ email });

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: result.message,
        data: result.data,
      })
    );
  } catch (error) {
    next(error);
  }
};

// -------------------------

export const resetPassword = async (req, res, next) => {
  try {
    const { email, otp, newPassword } = req.body;

    // Validate the input
    await validateResetPassword({ email, otp, newPassword });

    // Handle password reset
    const result = await handleResetPassword({ email, otp, newPassword });

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: result.message,
        data: null,
      })
    );
  } catch (error) {
    next(error);
  }
};

// -------------------------
