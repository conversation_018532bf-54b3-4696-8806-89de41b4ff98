import { getRoleRepo, getUser<PERSON><PERSON><PERSON>, ROLE_VALUES } from "../models/user/index.js";
import { RolesList } from "./bootstrap-data/index.js";
import { generateHashedPassword } from "./controllers/auth/auth-services/auth.service.js";
import { CustomLogger } from "./utils/logger/logger.js";
import { getGlobalRepo } from "../models/filter/globalFilter.entity.js";

export const InitializeBootstrap = async () => {
  await createRoles();

  await createAdmin();

  await insertFilterData();
};

const createRoles = async () => {
  try {
    const roleRepo = getRoleRepo();

    const existingRoles = await roleRepo.find({ select: ["value"] });
    const existingRoleValues = new Set(existingRoles.map((role) => role.value));

    const newRoles = RolesList.filter(
      (role) => !existingRoleValues.has(role.value)
    ).map((role) =>
      roleRepo.create({
        name: role.name,
        value: role.value,
        description: role.description,
      })
    );

    if (newRoles.length > 0) {
      await roleRepo.save(newRoles);
      CustomLogger.info(`Successfully inserted ${newRoles.length} new roles.`);
    } else {
      CustomLogger.info("No new roles to insert. All roles already exist.");
    }
  } catch (error) {
    CustomLogger.error(`Error creating roles: ${error.stack || error.message}`);
  }
};

const getAdminData = () => {
  return {
    NAME: process.env.APP_ADMIN_NAME || "",
    EMAIL: process.env.APP_ADMIN_MAIL || "",
    PASSWORD: process.env.APP_ADMIN_PASSWORD || "",
  };
};

const createAdmin = async () => {
  const { EMAIL, NAME, PASSWORD } = getAdminData();

  if (!EMAIL || !NAME || !PASSWORD) {
    const CustomError = {
      error: true,
      statusCode: 400,
      message: "Admin credentials are not fully provided...",
      path: "/bootstrap.js",
      errorId: Date.now(),
      timestamp: new Date().toISOString(),
    };

    CustomLogger.error(CustomError);
    return;
  }

  const userRepo = getUserRepo();
  const roleRepo = getRoleRepo();

  try {
    const existingUser = await userRepo.findOne({
      where: { email: EMAIL },
    });

    if (!existingUser) {
      let role = await roleRepo.findOne({
        where: { value: ROLE_VALUES.ADMIN },
      });

      if (!role) {
        CustomLogger.info("Admin role not found. Creating admin role...");
        try {
          role = await roleRepo.save({
            value: ROLE_VALUES.ADMIN,
            name: "Admin",
          });
          CustomLogger.info("Admin role created successfully.");
        } catch (error) {
          CustomLogger.error(
            `Error creating admin role: ${error.stack || error.message}`
          );
          return;
        }
      }

      const adminData = {
        email: EMAIL,
        name: NAME,
        password: await generateHashedPassword(PASSWORD),
        isVerified: true,
        role, // associate the role if needed
      };

      try {
        await userRepo.save(adminData);
        CustomLogger.info("Admin created successfully.");
      } catch (error) {
        CustomLogger.error(
          `Error saving admin user: ${error.stack || error.message}`
        );
        throw error;
      }
    } else {
      CustomLogger.info("Admin already exists in DB.");
    }
  } catch (err) {
    CustomLogger.error(
      `Unexpected error in createAdmin: ${err.stack || err.message}`
    );
    throw err;
  }
};

const insertFilterData = async () => {
  const globalRepo = getGlobalRepo();
  const existingFilters = await globalRepo.find();

  if (existingFilters.length > 0) {
    CustomLogger.info("Filter data already exists in DB.");
    return;
  }

  const filterDataList = [
    { name: "Clothing" },
    { name: "Shoes" },
    { name: "Accessories" },
    { name: "Cosmetic" },
    { name: "Hair Products" },
    { name: "Jewelry" },
    { name: "Electronics" },
    { name: "Grooming" },
    { name: "General" },
  ];

  // Flatten to a single array of strings
  const flatCategoryList = filterDataList.map((item) => item.name);

  const newFilter = globalRepo.create({
    menCategories: flatCategoryList,
    womenCategories: flatCategoryList,
    otherCategories: null,
  });

  await globalRepo.save(newFilter);
  CustomLogger.info("Filter data inserted successfully.");
};
