import { OpenAI } from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const askOpenAI = async (prompt) => {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo", // fastest and cheapest model
      messages: [
        {
          role: "system",
          content:
            "You are a helpful assistant that formats raw CSV product rows into clean product JSON format for a database.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.2,
    });

    const answer = response.choices[0]?.message?.content?.trim();
    return answer;
  } catch (error) {
    console.error("OpenAI error:", error);
    throw new Error("Failed to get response from OpenAI");
  }
};
