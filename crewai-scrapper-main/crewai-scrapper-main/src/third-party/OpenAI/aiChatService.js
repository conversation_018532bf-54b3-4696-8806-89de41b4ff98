import { OpenAI } from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const generateSearchTermFromConversation = async (messages, userMessage) => {
  try {
    console.log(`[AI Service] Processing ${messages.length} messages for context`);
    console.log(`[AI Service] User message: ${userMessage}`);

    const conversationHistory = messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    console.log(`[AI Service] Conversation history:`, conversationHistory);

    const systemPrompt = `You are an AI shopping assistant that helps users find products. Your task is to:
1. Analyze the conversation history and the user's latest message
2. Generate an optimized search term for product search
3. Provide a helpful response to continue the conversation

Based on the conversation context and the user's latest message, generate:
1. A concise, optimized search term (2-5 words) that would find relevant products
2. A conversational response that acknowledges the user's request and explains what you're searching for

Return your response in this JSON format:
{
  "searchTerm": "optimized search term",
  "response": "conversational response to user"
}

Focus on extracting the core product intent from the conversation. If the user is refining their search, incorporate previous context.
${conversationHistory.length > 0 ? 'IMPORTANT: Use the conversation history to understand the context and build upon previous searches.' : 'This is the first message in the conversation.'}`;

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: systemPrompt },
        ...conversationHistory,
        { role: "user", content: userMessage }
      ],
      temperature: 0.3,
      max_tokens: 300,
    });

    const aiResponseContent = response.choices[0]?.message?.content?.trim();
    console.log(`[AI Service] Raw AI response:`, aiResponseContent);

    const result = JSON.parse(aiResponseContent);
    console.log(`[AI Service] Parsed result:`, result);

    return {
      searchTerm: result.searchTerm,
      response: result.response
    };
  } catch (error) {
    console.error("AI Chat Service error:", error);
    console.error("Error details:", error.message);

    // Fallback response - extract key terms from user message
    const fallbackSearchTerm = userMessage.length > 50 ?
      userMessage.substring(0, 50).trim() :
      userMessage.trim();

    return {
      searchTerm: fallbackSearchTerm,
      response: "I'll help you find products related to your request. Let me search for that."
    };
  }
};

export const generateConversationTitle = async (initialMessage) => {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "Generate a short, descriptive title (3-6 words) for a shopping conversation based on the user's initial search. Return only the title, no quotes or extra text."
        },
        {
          role: "user",
          content: initialMessage
        }
      ],
      temperature: 0.5,
      max_tokens: 20,
    });

    return response.choices[0]?.message?.content?.trim() || "Product Search";
  } catch (error) {
    console.error("Title generation error:", error);
    return "Product Search";
  }
};

export const generateContextualResponse = async (messages, searchResults) => {
  try {
    const conversationHistory = messages.slice(-5).map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    const systemPrompt = `You are a helpful shopping assistant. Based on the conversation history and search results, provide a brief, conversational response that:
1. Acknowledges what was found
2. Highlights key insights about the products
3. Asks a follow-up question to help refine the search

Keep the response concise (1-2 sentences) and conversational.`;

    const searchSummary = `Found ${searchResults.length} products. Price range: ${searchResults.length > 0 ? 'Various prices available' : 'No products found'}.`;

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: systemPrompt },
        ...conversationHistory,
        { role: "user", content: `Search results: ${searchSummary}` }
      ],
      temperature: 0.7,
      max_tokens: 100,
    });

    return response.choices[0]?.message?.content?.trim() || "I found some products for you. Would you like to refine your search?";
  } catch (error) {
    console.error("Contextual response error:", error);
    return "I found some products for you. Would you like to refine your search?";
  }
};
