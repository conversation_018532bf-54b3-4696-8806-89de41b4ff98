import { Resend } from "resend";
import { CustomLogger } from "../../utils/logger/index.js";

const resend = new Resend(process.env.RESEND_API_KEY); // instantiate with your API key

export const SendEmail = async (email, subject, html) => {
  try {
    const fromEmail = process.env.RESEND_FROM_EMAIL;

    const data = await resend.emails.send({
      from: fromEmail,
      to: email,
      subject,
      html,
    });

    return {
      data,
      success: true,
      error: null,
      message: "Email sent successfully",
    };
  } catch (error) {
    CustomLogger.error(error);
    return {
      data: null,
      success: false,
      error: error,
      message: error.message || "Email sending failed",
    };
  }
};
