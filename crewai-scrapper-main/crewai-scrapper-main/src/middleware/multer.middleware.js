import multer from "multer";
import path from "path";

const storage = multer.memoryStorage();

const csvFileFilter = (req, file, cb) => {
  const extension = path.extname(file.originalname).toLowerCase();
  if (extension !== ".csv") {
    return cb(new Error("Only CSV files are allowed"), false);
  }
  cb(null, true);
};

export const multerUploadMiddleware = multer({
  storage,
  fileFilter: csvFileFilter,
  limits: { files: 1 },
});
