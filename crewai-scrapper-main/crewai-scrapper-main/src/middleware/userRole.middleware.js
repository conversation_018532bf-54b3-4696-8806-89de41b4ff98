import { StatusCodes } from "http-status-codes";
import { CustomApiError } from "../utils/errors/index.js";
import { getRoleRepo, ROLE_VALUES } from "../../models/user/index.js";

export const userRoleMiddleware = async (req, res, next) => {
  const user = req.user;

  if (!user) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "User authentication required"
    );
  }

  // Check if user has admin role
  const roleRepo = getRoleRepo();
  const adminRole = await roleRepo.findOne({
    where: { value: ROLE_VALUES.ADMIN },
  });

  if (!adminRole) {
    throw new CustomApiError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      "Admin role not found in system"
    );
  }

  // For now, we'll check if the user is the admin user created in bootstrap
  // In a more complex system, you'd have a user-role relationship
  const adminEmail = process.env.APP_ADMIN_MAIL;
  
  if (!adminEmail || user.email !== adminEmail) {
    throw new CustomApiError(
      StatusCodes.FORBIDDEN,
      "Access denied. Admin privileges required."
    );
  }

  next();
};
