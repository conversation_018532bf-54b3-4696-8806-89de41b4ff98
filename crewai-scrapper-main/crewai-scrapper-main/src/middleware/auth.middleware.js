import { StatusCodes } from "http-status-codes";
import { CustomApiError } from "../utils/errors/index.js";
import { validateToken } from "../controllers/auth/auth-services/auth.service.js";

export const userAuthMiddleware = async (req, res, next) => {
  const accessToken =
    req.headers?.authorization?.split(" ")[1] ||
    req.headers?.Authorization?.split(" ")[1];

  if (!accessToken || typeof accessToken !== "string") {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Access token is missing or invalid"
    );
  }

  const { success: isAccessTokenValid, user } = await validateToken(
    accessToken,
    "accessToken"
  );

  if (!isAccessTokenValid || !user) {
    throw new CustomApiError(
      StatusCodes.UNAUTHORIZED,
      "Invalid or expired token"
    );
  }

  req.user = user;
  next();
};
