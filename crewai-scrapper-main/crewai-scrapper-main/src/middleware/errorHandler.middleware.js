import { StatusCodes } from "http-status-codes";
import { CustomLogger } from "../utils/logger/logger.js";
import { CustomApiError } from "../utils/errors/CustomApiError.js";

export const errorHandlerMiddleware = (err, req, res, next) => {
  const errorResponse = {
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
    statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
    message: "An unexpected error occurred",
    errors: [],
    success: false,
    data: null,
  };

  // Custom error handling
  if (err.name === "CastError") {
    errorResponse.statusCode = StatusCodes.NOT_FOUND;
    errorResponse.message = "Resource not found";
  } else if (err.code === 11000) {
    errorResponse.statusCode = StatusCodes.BAD_REQUEST;
    errorResponse.message = "Duplicate field value entered";
  } else if (err.name === "ValidationError") {
    errorResponse.statusCode = StatusCodes.BAD_REQUEST;
    errorResponse.message = Object.values(err.errors)
      .map((val) => val.message)
      .join(", ");
  } else if (err.name === "TimeoutError") {
    errorResponse.statusCode = StatusCodes.REQUEST_TIMEOUT;
    errorResponse.message = "Request timeout - the page took too long to load";
  } else if (err.code === "ENOTFOUND" || err.code === "ECONNREFUSED") {
    errorResponse.statusCode = StatusCodes.SERVICE_UNAVAILABLE;
    errorResponse.message =
      "Network error - unable to reach the target website";
  } else if (err instanceof CustomApiError) {
    errorResponse.statusCode = err.statusCode || StatusCodes.BAD_REQUEST;
    errorResponse.message = err.message;
    errorResponse.errors = err.errors || [];
  } else {
    // Unhandled error
    errorResponse.message = err.message || errorResponse.message;
  }

  // Log the sanitized error
  CustomLogger.error("Handled Error", {
    name: err.name,
    message: errorResponse.message,
    method: req.method,
    path: req.path,
    statusCode: errorResponse.statusCode,
  });

  return res.status(errorResponse.statusCode).json(errorResponse);
};
