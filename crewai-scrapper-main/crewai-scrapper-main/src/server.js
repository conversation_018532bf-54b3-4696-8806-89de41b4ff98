import "express-async-errors"; // ensure its on top
import "dotenv/config";
import "reflect-metadata";

import express from "express";
import cors from "cors";
import helmet from "helmet";
import { rateLimit } from "express-rate-limit";

import { CustomLogger } from "./utils/logger/index.js";
import { errorHandlerMiddleware } from "./middleware/errorHandler.middleware.js";
import { notFoundMiddleware } from "./middleware/notFound.middleware.js";
import { setupAndInitializeDatabase } from "../config/index.js";
import { InitializeBootstrap } from "./bootstrap.js";
import { rootRouter } from "./routes/_rootRouter.js";
import { ApiUrlLoggerMiddleware } from "./middleware/apiUrlLogger.middleware.js";

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());
app.use(cors());

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: "Too many requests from this IP, please try again later.",
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// api logger middleware
app.use(ApiUrlLoggerMiddleware);

// Health check endpoint

app.get("/", (req, res) => {
  res.status(200).send("Welcome to lvendora AI API");
});

app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    mode: process.env.NODE_ENV === "production" || "development",
  });
});

// API root router
app.use("/api", rootRouter);

// Error handling middleware
app.use(errorHandlerMiddleware);

// 404 handler
app.use(notFoundMiddleware);

setupAndInitializeDatabase()
  .then(async () => {
    CustomLogger.info("Database connected successfully");

    await InitializeBootstrap();

    app.listen(PORT, () => {
      CustomLogger.info(`Server running on http://localhost:${PORT}`);
      CustomLogger.info(`Server running in ${process.env.NODE_ENV} mode`);
    });
  })
  .catch((error) => {
    CustomLogger.error(
      `Database connection failed: ${error.stack || error.message}`
    );
    process.exit(1);
  });
