import { Router } from "express";
import {
  getNewAccessToken,
  loginUser,
  logout,
  registerUser,
  verifyOTP,
  resendOtp,
  forgetPassword,
  resetPassword,
} from "../controllers/auth/auth.controller.js";
import { userAuthMiddleware } from "../middleware/auth.middleware.js";

export const authRouter = Router();

// Public routes
authRouter.post("/register", registerUser);
authRouter.post("/login", loginUser);
authRouter.post("/verify-otp", verifyOTP);
authRouter.post("/resend-otp", resendOtp);
authRouter.post("/forget-password", forgetPassword);
authRouter.post("/reset-password", resetPassword);

// Protected routes
authRouter.post("/logout", userAuthMiddleware, logout);
authRouter.get("/refresh-token", getNewAccessToken);
