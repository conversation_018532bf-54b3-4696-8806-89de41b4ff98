import { Router } from "express";
import { userRoleMiddleware, userAuthMiddleware } from "../middleware/index.js";
import {
  addBulkProducts,
  addBulkVendors,
  addSingleProduct,
  addSingleVendor,
  getAllGlobalFilters,
  getAllProducts,
  getGlobalCategories,
  getVendorsList,
  updateGlobalFilters,
  updateGlobalCategories,
} from "../controllers/admin/admin.controller.js";
import { multerUploadMiddleware } from "../middleware/multer.middleware.js";
export const adminRouter = Router();

// ----------------------------- Products -----------------------------
adminRouter.get(
  "/admin/products",
  userAuthMiddleware,
  userRoleMiddleware,
  getAllProducts
);

adminRouter.post(
  "/admin/products/single",
  userAuthMiddleware,
  userRoleMiddleware,
  addSingleProduct
);

// ----------------------------- Vendors -----------------------------
adminRouter.get(
  "/admin/vendors",
  userAuthMiddleware,
  userRoleMiddleware,
  getVendorsList
);

adminRouter.post(
  "/admin/vendors/single",
  userAuthMiddleware,
  userRoleMiddleware,
  addSingleVendor
);

// ----------------------------- Bulk Uploads -----------------------------
// --Products--
adminRouter.post(
  "/admin/products/bulk",
  userAuthMiddleware,
  userRoleMiddleware,
  multerUploadMiddleware.single("file"),
  addBulkProducts
);

// --Vendors--
adminRouter.post(
  "/admin/vendors/bulk",
  userAuthMiddleware,
  userRoleMiddleware,
  multerUploadMiddleware.single("file"),
  addBulkVendors
);

// ----------------------------- Global Filters -----------------------------
adminRouter.get(
  "/admin/global-filter",
  userAuthMiddleware,
  userRoleMiddleware,
  getAllGlobalFilters
);

adminRouter.put(
  "/admin/global-filter",
  userAuthMiddleware,
  userRoleMiddleware,
  updateGlobalFilters
);

adminRouter.get(
  "/admin/global-categories",
  userAuthMiddleware,
  userRoleMiddleware,
  getGlobalCategories
);

adminRouter.put(
  "/admin/global-categories",
  userAuthMiddleware,
  userRoleMiddleware,
  updateGlobalCategories
);
