import express from "express";
import {
  alibabaScrapeProductListThroughAlibabaApi,
  alibabaScrapeProductsList,
  scrapeStaticDataFromJSON,
  scrapeSuppliersFromThomasNet,
  scrapeVendorsFromFashionGo,
} from "../controllers/scrapper/scraper.controller.js";

export const scraperRouter = express.Router();

// =====================ALIBABA=====================
scraperRouter.get("/scrape-alibaba/products-list", alibabaScrapeProductsList);

scraperRouter.get(
  "/scrape-alibaba-url/products-list",
  alibabaScrapeProductListThroughAlibabaApi
);

// =====================STATIC DATA=====================
scraperRouter.get(
  "/scrape-static-data/products-list",
  scrapeStaticDataFromJSON
);

// =====================THOMASNET=====================
scraperRouter.get(
  "/scrape-thomas/suppliers-list",
  scrapeSuppliersFromThomasNet
);

// =====================FASHIONGO=====================
scraperRouter.get("/scrape-fashiongo/vendors-list", scrapeVendorsFromFashionGo);
