import { Router } from "express";
import {
  clearSearchHistory,
  deleteSearchHistory,
  getSearchHistory,
} from "../controllers/history/history.controller.js";
import { userAuthMiddleware } from "../middleware/auth.middleware.js";

export const historyRouter = Router();

//historyRouter.post("/history", userAuthMiddleware, saveSearchHistory);
historyRouter.get("/history", userAuthMiddleware, getSearchHistory);
historyRouter.delete("/history/:id", userAuthMiddleware, deleteSearchHistory);
historyRouter.delete("/history", userAuthMiddleware, clearSearchHistory);
