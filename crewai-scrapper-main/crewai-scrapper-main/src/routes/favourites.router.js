// src/routes/favourites.router.ts
import { Router } from "express";
import {
  addToFavourites,
  getFavourites,
  removeFavourite,
} from "../controllers/favourites/favourites.controller.js";
import { userAuthMiddleware } from "../middleware/auth.middleware.js";

export const favouriteRouter = Router();

favouriteRouter.post("/favourites", userAuthMiddleware, addToFavourites);

favouriteRouter.get("/favourites", userAuthMiddleware, getFavourites);

favouriteRouter.delete(
  "/favourites/:productId",
  userAuthMiddleware,
  removeFavourite
);
