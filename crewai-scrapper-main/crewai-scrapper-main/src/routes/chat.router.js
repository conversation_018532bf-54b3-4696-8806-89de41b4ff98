import { Router } from "express";
import { userAuthMiddleware } from "../middleware/auth.middleware.js";
import {
  createConversation,
  addMessage,
  getConversation,
  getUserConversations,
  deleteConversation
} from "../controllers/chat/chat.controller.js";

export const chatRouter = Router();

// All chat routes require authentication
chatRouter.use(userAuthMiddleware);

// Create new conversation
chatRouter.post("/chat/conversations", createConversation);

// Add message to conversation
chatRouter.post("/chat/conversations/:conversationId/messages", addMessage);

// Get specific conversation with messages
chatRouter.get("/chat/conversations/:conversationId", getConversation);

// Get user's conversations
chatRouter.get("/chat/conversations", getUserConversations);

// Delete conversation
chatRouter.delete("/chat/conversations/:conversationId", deleteConversation);
