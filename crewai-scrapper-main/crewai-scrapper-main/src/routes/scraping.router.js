import { Router } from "express";
import { scrapeAndSaveAlibabaProducts, chatIntegratedSearch } from "../controllers/scraping/scraping.controller.js";
import { userAuthMiddleware } from "../middleware/auth.middleware.js";

export const scrapingRouter = Router();

// ----------------------------- Scraping -----------------------------
scrapingRouter.get("/scraping", scrapeAndSaveAlibabaProducts);

// Chat-integrated search endpoint
scrapingRouter.post("/scraping/chat-search", userAuthMiddleware, chatIntegratedSearch);
