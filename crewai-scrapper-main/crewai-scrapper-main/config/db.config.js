import { DataSource } from "typeorm";
import path from "path";
import { fileURLToPath } from "url";
import { CustomLogger } from "../src/utils/logger/index.js";

// ES modules __dirname polyfill
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function isValidDatabaseName(name) {
  return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
}

export const AppDataSource = new DataSource({
  type: "postgres",
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  entities: [path.join(__dirname, "../models/**/*.entity.js")],
  migrations: [path.join(__dirname, "../migrations/*.js")],
  synchronize: process.env.DB_SYNCHRONIZE === "true",
  logging: process.env.DB_LOGGING === "true",
  extra: {
    ssl: process.env.DB_SSL === "true" ? { rejectUnauthorized: false } : false,
  },
});

export async function setupAndInitializeDatabase() {
  const requiredEnvVars = [
    "DB_HOST",
    "DB_PORT",
    "DB_USERNAME",
    "DB_PASSWORD",
    "DB_DATABASE",
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing environment variable: ${envVar}`);
    }
  }

  const dbName = process.env.DB_DATABASE;

  if (!isValidDatabaseName(dbName)) {
    throw new Error(
      `Invalid database name: ${dbName}. Only alphanumeric characters and underscores are allowed.`
    );
  }

  // Step 1: Ensure the database exists
  const baseConfig = {
    type: "postgres",
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    synchronize: false,
  };

  const baseDataSource = new DataSource({
    ...baseConfig,
    database: "postgres",
  });

  try {
    await baseDataSource.initialize();
    CustomLogger.info("Connected to default PostgreSQL database");

    const dbExists = await baseDataSource.query(
      `SELECT 1 FROM pg_database WHERE datname = $1`,
      [dbName]
    );

    if (dbExists.length === 0) {
      CustomLogger.info(`Creating database: ${dbName}`);
      await baseDataSource.query(`CREATE DATABASE "${dbName}"`);
    } else {
      CustomLogger.info(`Database ${dbName} already exists`);
    }

    await baseDataSource.destroy();
    CustomLogger.info("Closed default database connection");

    // Step 2: Initialize the main DataSource
    await AppDataSource.initialize();
    CustomLogger.info("Application database connection established");

    return AppDataSource;
  } catch (error) {
    CustomLogger.error("Database setup/initialization failed:", error);
    if (error instanceof Error) {
      CustomLogger.error(`Error details: ${error.message}\n${error.stack}`);
    }
    process.exit(1);
  }
}
